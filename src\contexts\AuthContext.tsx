import React, { createContext, useContext, useEffect, useState } from "react";
import { useRouter } from "next/router";
import QRCode from "qrcode";
import {
  signIn,
  signOut,
  getCurrentUser,
  fetchAuthSession,
  confirmSignIn,
  updateMFAPreference,
} from "aws-amplify/auth";

import { slugs } from "@constants/slugs";
import {
  APP_NAME,
  LOGIN_STAGE,
  SETUP_TOTP_STAGE,
  VERIFY_TOTP_STAGE,
  NEW_PASSWORD_STEP,
  TOTP_SETUP,
  VERIFY_TOTP,
  INVALID_SESSION,
} from "@constants";
import { tempPass } from "@constants/cognito";

export type Stage = "LOGIN_STAGE" | "SETUP_TOTP_STAGE" | "VERIFY_TOTP_STAGE";

interface AuthContextProps {
  user: any;
  loading: boolean;
  error: string;
  setError: React.Dispatch<React.SetStateAction<string>>;
  loginUser: (username: string, password: string) => Promise<void>;
  logoutUser: () => Promise<void>;
  checkUserSession: () => Promise<void>;
  qrDataUrl: string;
  verifyTotp: (totpCode: string) => Promise<void>;
  processLoader: boolean;
  setProcessLoader: React.Dispatch<React.SetStateAction<boolean>>;
  authRoutes: string[];
  stage: Stage;
  handleStageChange: (newStage: Stage) => void;
}

const AuthContext = createContext<AuthContextProps | undefined>(undefined);

const authRoutes: string[] = [slugs.login];

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [stage, setStage] = useState<Stage>(LOGIN_STAGE);
  const [error, setError] = useState<string>("");
  const [qrDataUrl, setQRDataUrl] = useState<string>("");
  const [processLoader, setProcessLoader] = useState<boolean>(false);
  const router = useRouter();

  const logError = (context: string, error: any) => {
    console.error(`${context} - Error:`, error?.message || error);
    if (error?.code) {
      console.error(`${context} - Error Code:`, error.code);
    }
  };

  const handleStageChange = (newStage: Stage) => {
    setStage(newStage);
  };

  const fetchUser = async () => {
    try {
      setLoading(true);
      const userData = await getCurrentUser();
      setUser(userData);
      if (typeof window !== "undefined") {
        localStorage.setItem("userId", userData.userId);
      }
    } catch (error) {
      logError("Fetching User", error);
    } finally {
      setLoading(false);
    }
  };

  const checkUserSession = async () => {
    try {
      setLoading(true);
      await fetchAuthSession();
      await fetchUser();
    } catch (err) {
      logError("User Session Check", err);
      setUser(null);
      if (router.asPath.replace("/", "") !== slugs.login) {
        handleStageChange(LOGIN_STAGE);
        router.replace(`/${slugs.login}`);
      }
    } finally {
      setLoading(false);
    }
  };

  const generateQR = async (
    username: string,
    secret: string,
    appName: string
  ) => {
    setLoading(true);
    const qrCodeUrl = `otpauth://totp/${username}?secret=${secret}&issuer=${appName}`;
    const qrCodeDataUrl = await QRCode.toDataURL(qrCodeUrl);
    setQRDataUrl(qrCodeDataUrl);
    setLoading(false);
  };

  const loginUser = async (username: string, password: string) => {
    try {
      const { nextStep } = await signIn({ username, password });

      const generateSecretWithQR = async (nextStep: any) => {
        const secretCode = nextStep.totpSetupDetails?.sharedSecret;
        if (secretCode) {
          setProcessLoader(true);
          await generateQR(username, secretCode, APP_NAME);
          setProcessLoader(false);
          handleStageChange(SETUP_TOTP_STAGE);
        } else {
          logError("TOTP setup:", "Details missing");
          setError("Failed to set up TOTP.");
          return;
        }
      };

      if (nextStep.signInStep === TOTP_SETUP) {
        await generateSecretWithQR(nextStep);
      }

      if (nextStep.signInStep === VERIFY_TOTP) {
        handleStageChange(VERIFY_TOTP_STAGE);
      }

      if (nextStep.signInStep === NEW_PASSWORD_STEP) {
        await confirmSignIn({ challengeResponse: tempPass });
        handleStageChange(LOGIN_STAGE);
        router.replace(`/${slugs.login}`);
      }
    } catch (error: any) {
      logError("Error during login:", error);
      setError(error.message || "Oops, something went wrong!");
    }
  };

  const verifyTotp = async (totpCode: string) => {
    try {
      setProcessLoader(true);
      setError("");
      await confirmSignIn({ challengeResponse: totpCode });
      await updateMFAPreference({ totp: "PREFERRED" });
      await fetchUser();
      router.replace(slugs.home);
    } catch (error: any) {
      logError("TOTP Verification", error);
      setError(error.message || "Oops, something went wrong!");
      if (error.message === INVALID_SESSION) {
        setTimeout(() => handleStageChange(LOGIN_STAGE), 1000);
      }
    } finally {
      setProcessLoader(false);
    }
  };

  const logoutUser = async () => {
    try {
      setLoading(true);
      setError("");
      await signOut();
      setUser(null);
      handleStageChange(LOGIN_STAGE);
      router.replace(`/${slugs.login}`);
      if (typeof window !== "undefined") {
        localStorage.clear();
        sessionStorage.clear();
      }
    } catch (error: any) {
      logError("Logout", error);
      setError(error.message || "Oops, something went wrong!");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const currentPath = router.asPath;
    const hasUserId = user?.userId || localStorage.getItem("userId");

    if (router.isReady) {
      if (!authRoutes.includes(currentPath.replace("/", ""))) {
        if (hasUserId) {
          router.replace(slugs.home);
        } else {
          handleStageChange(LOGIN_STAGE);
          router.replace(`/${slugs.login}`);
        }
      } else if (authRoutes.includes(currentPath.replace("/", ""))) {
        if (hasUserId) {
          router.replace(slugs.home);
        }
      }
    }
  }, [router.asPath]);

  return (
    <AuthContext.Provider
      value={{
        user,
        qrDataUrl,
        loading,
        error,
        stage,
        handleStageChange,
        authRoutes,
        setError,
        loginUser,
        logoutUser,
        checkUserSession,
        verifyTotp,
        processLoader,
        setProcessLoader,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuthState = (): AuthContextProps => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuthState must be used within an AuthProvider");
  }
  return context;
};
