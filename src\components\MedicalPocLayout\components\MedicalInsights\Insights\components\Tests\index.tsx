import React from "react";
import { motion } from "framer-motion";
import cx from "classnames";
import { TestsProps } from "./typings";

const DisplayTests = (tests: any, page_no: number): JSX.Element => {
  const filteredTests = tests?.find(
    (item: any) => item?.page_no === page_no
  )?.tests;
  return (
    <div className="flex flex-col gap-y-2">
      {filteredTests?.length > 0 ? (
        filteredTests?.map((test: any, index: number) => (
          <div className="flex flex-col" key={index}>
            <div className="flex gap-x-2 items-center">
              <span className="font-semibold">Date: </span>
              <span>{test?.date || "-"}</span>
            </div>
            <div className="flex gap-x-2 items-center">
              <span className="font-semibold">Name: </span>
              <span>{test?.name || "-"}</span>
            </div>
          </div>
        ))
      ) : (
        <span>-</span>
      )}
    </div>
  );
};

const Tests: React.FC<TestsProps> = ({ tests, viewPDF }) => {
  const modifiedTests: any = [];

  tests?.forEach((item: any) => {
    item?.tests?.forEach((it: any) => {
      modifiedTests.push({
        document_name: item.name,
        page_no: it.page_no,
        tests: item.tests,
      });
    });
  });

  return (
    <div className="overflow-hidden rounded-b-xl">
      {modifiedTests?.length === 0 ? (
        <motion.div
          className="text-center border m-4 rounded-lg text-brand-secondary font-semibold px-12 py-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          No tests are available.
        </motion.div>
      ) : (
        <div className="overflow-x-auto c-scroll">
          <motion.table
            className="w-full text-left border-collapse"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <thead>
              <tr className="bg-brand-secondary/30 font-medium">
                <th className="px-12 py-2">Document Name</th>
                <th className="px-12 py-2 text-center">Page #</th>
                <th className="px-12 py-2">Tests</th>
              </tr>
            </thead>
            <tbody>
              {modifiedTests?.map(
                ({ document_name, page_no, tests }: any, index: number) => (
                  <motion.tr
                    key={index}
                    className={cx(
                      { "bg-brand-gray": index % 2 !== 0 },
                      { "bg-white": index % 2 === 0 }
                    )}
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <td className="px-12 py-3 text-brand-secondary font-semibold cursor-pointer underline break-all">
                      <span onClick={() => viewPDF(1, document_name)}>
                        {document_name || "-"}
                      </span>
                    </td>
                    <td className="px-12 py-3 text-brand-secondary font-semibold cursor-pointer underline text-center">
                      <span onClick={() => viewPDF(page_no, document_name)}>
                        {page_no || "-"}
                      </span>
                    </td>
                    <td className="px-12 py-3">
                      {DisplayTests(tests, page_no)}
                    </td>
                  </motion.tr>
                )
              )}
            </tbody>
          </motion.table>
        </div>
      )}
    </div>
  );
};

export default Tests;
