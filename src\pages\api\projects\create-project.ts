import { pool } from "@utils/dbConnect";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method is not allowed" });
  }

  const { userId, projectId, projectName, totalPages, status } = JSON.parse(
    req.body
  );
  try {
    const client = await pool.connect();
    await pool.query(
      "INSERT INTO user_projects (user_id, project_id, project_name, total_pages, status) VALUES ($1, $2, $3, $4, $5)",
      [userId, projectId, projectName.trim(), totalPages, status]
    );
    client.release();
    res.status(201).json({ id: projectId });
  } catch (error: any) {
    console.error(error.message);
    res.status(500).json({ error: error.message });
  }
}
