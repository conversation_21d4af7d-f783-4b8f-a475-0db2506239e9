import React, { useEffect, useRef, useState } from "react";
import cx from "classnames";
import { motion } from "framer-motion";
import { Raleway } from "next/font/google";
import { Arrow } from "@public/assets/icons";
import { QAProps, QueryState } from "./typings";
import { Questions } from "./questions";
import { useAuthState } from "@contexts/AuthContext";
import { buckets } from "@constants/buckets";
import DOMPurify from "dompurify";
import { fetchCsrfToken } from "@utils";

const raleway = Raleway({
  display: "swap",
  weight: ["400", "500", "600"],
  subsets: ["latin"],
});

const QA: React.FC<QAProps> = ({
  isSample,
  sample,
  project,
  resultQueries,
  addQueryResult,
  handleResultQueries,
  counter,
  handleCounter,
  queryLoader,
  handleQueryLoader,
  containerRef,
  handleQAModal,
  handleQAError,
  resetCounter,
}) => {
  const [currentQuery, setCurrentQuery] = useState<string>("");
  const inputRef = useRef<HTMLInputElement>(null);
  const { user } = useAuthState();
  const noResultQueries = resultQueries?.length === 0;
  const fetchQueryResult = async (suggestedQuery?: string | any) => {
    try {
      handleQAError({ error: false, message: "" });
      handleQueryLoader(true);

      const projectPath = isSample
        ? `s3://${buckets.mi}/sample-data/sample${sample?.sampleInd}_v1.1/request`
        : `s3://${buckets.mi}/user-data/${user?.userId}/${project?.project_id}/request`;
      const csrfToken = await fetchCsrfToken();
      const response = await fetch(`/api/qna-extract`, {
        method: "POST",
        headers: {
          "X-CSRF-Token": csrfToken,
        },
        body: JSON.stringify({
          project_path: projectPath,
          input_query: suggestedQuery || currentQuery,
        }),
      });
      if (response.ok) {
        const result = await response.json();
        addQueryResult(result);
      } else {
        const errorResponse = await response.json();
        throw new Error(errorResponse.message);
      }
    } catch (error: any) {
      handleQAError({ error: true, message: error?.message });
      handleQAModal(true);
      setCurrentQuery("");
      handleResultQueries([]);
      resetCounter();
      handleQueryLoader(false);
    }
  };
  const handleQuery = (event: React.ChangeEvent<HTMLInputElement>) => {
    const query = event.target.value;
    setCurrentQuery(query);
  };

  const handleQueryResult = async (
    event?: React.FormEvent<HTMLFormElement> | null,
    suggestedQuery?: string | any
  ) => {
    if (event) {
      event.preventDefault();
    }
    handleCounter();
    handleResultQueries((prevQueries: QueryState[]) => [
      ...prevQueries,
      { id: counter + 1, query: suggestedQuery || currentQuery, result: "" },
    ]);
    if (!suggestedQuery) setCurrentQuery("");
    await fetchQueryResult(suggestedQuery);
    handleQueryLoader(false);
    if (inputRef && inputRef.current) {
      inputRef.current.focus();
    }
  };
  useEffect(() => {
    if (inputRef && inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (containerRef && containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
  }, []);

  console.log("resultQueries", resultQueries);

  return (
    <>
      <motion.div
        className="flex flex-col justify-between h-[800px] w-full"
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div
          ref={containerRef}
          className={cx("flex flex-col w-full overflow-y-scroll c-scroll p-8", {
            "justify-center items-center h-screen": noResultQueries,
            "qa-height": !noResultQueries,
          })}
        >
          {noResultQueries ? (
            <motion.span
              className="text-[28px] text-gradient-black font-semibold"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            >
              How can I help you today?
            </motion.span>
          ) : (
            <div className="flex flex-col space-y-2 w-full">
              {resultQueries?.map(
                ({ id, query, result }: QueryState, index: number) => (
                  <div
                    className="flex flex-col space-y-3 bshadow-sm rounded-md bg-brand-white p-2.5"
                    key={id}
                  >
                    <span className="text-[#353535] font-semibold text-2xl">
                      {query}
                    </span>
                    <span className="text-gradient-black text-[22px] leading-8">
                      {queryLoader && index === resultQueries?.length - 1 ? (
                        <div className="query-loader"></div>
                      ) : (
                        <span
                          dangerouslySetInnerHTML={{
                            __html: result
                              ? DOMPurify.sanitize(
                                  result.replaceAll(/\n/g, "<br/>")
                                )
                              : "-",
                          }}
                        />
                      )}
                    </span>
                  </div>
                )
              )}
            </div>
          )}
        </div>
        <div className="pt-4 pb-2 px-8">
          <div className="flex gap-4 justify-center items-center flex-wrap text-[14px]">
            <button
              className="suggestion"
              onClick={() => {
                handleQueryResult(null, Questions.one);
              }}
              disabled={queryLoader}
            >
              {Questions.one}
            </button>
            <button
              className="suggestion"
              onClick={() => {
                handleQueryResult(null, Questions.two);
              }}
              disabled={queryLoader}
            >
              {Questions.two}
            </button>
            <button
              className="suggestion"
              onClick={() => {
                handleQueryResult(null, Questions.three);
              }}
              disabled={queryLoader}
            >
              {Questions.three}
            </button>
          </div>
        </div>
        <div
          className={`${raleway.className} flex items-center justify-center gap-5 p-6 w-full overflow-hidden`}
        >
          <motion.form
            className="flex justify-between items-center border border-[#858585] rounded-md max-w-xl py-2 px-2 w-full"
            onSubmit={handleQueryResult}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <input
              ref={inputRef}
              className="border-none focus:outline-none bg-transparent text-sm text-gradient-black placeholder:text-[#858585] w-full mx-2"
              placeholder="Type your question..."
              value={currentQuery}
              onChange={handleQuery}
            />
            <button
              type="submit"
              className={cx(
                "flex justify-center items-center p-2 rounded-[3px] cursor-pointer disabled:pointer-events-none disabled:bg-[#EAEAEA]",
                {
                  "ficon bg-brand-secondary":
                    !!currentQuery &&
                    !queryLoader &&
                    currentQuery?.trim() !== "",
                }
              )}
              disabled={
                !currentQuery || queryLoader || currentQuery?.trim() === ""
              }
            >
              <Arrow />
            </button>
          </motion.form>
        </div>
      </motion.div>
    </>
  );
};

export default QA;
