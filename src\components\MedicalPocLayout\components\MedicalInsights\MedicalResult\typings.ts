export interface Chronology {
  date: string;
  event: string;
  document_name: string;
  hospital_name: string;
  doctor_name: string;
  page_no: number;
}
export interface Test {
  date: string;
  name: string;
}
export interface MedicalTest {
  page_no: number;
  tests: Test[];
}

export interface Medication {
  name: string;
  dosage: string;
}

export interface Entity {
  page_no: number;
  diagnosis: {
    allergies: string[];
    pmh: string[];
    others: string[];
  };
  treatments: {
    pmh: string[];
    others: string[];
  };
  tests: MedicalTest[];
  medications: {
    pmh: Medication[];
    others: Medication[];
  };
}

export interface PHIDates {
  injury_dates: string[];
  admission_dates: string[];
  discharge_dates: string[];
}
export interface MedicalDocument {
  name: string;
  summary: string;
  phi_dates: PHIDates;
  medical_entities: Entity[];
}

export interface Summary {
  name: string;
  summary: string;
}

export interface GeneralHistory {
  family_history: {
    page_no: number | null;
    values: {
      [key: string]: string;
    };
  };
  psychiatric_injury: {
    page_no: number | null;
    values: string[];
  };
  social_history: {
    page_no: number | null;
    values: {
      smoking: string;
      alcohol: string;
      tobacco: string;
    };
  };
}

export interface PatientDemographics {
  patient_name: string;
  date_of_birth: string;
  age: string;
  gender: string;
  bmi: string;
  height: {
    value: string;
    date: string;
  };
  weight: {
    value: string;
    date: string;
  };
}

export interface MedicalResponse {
  summary: string;
  document_name: string;
  document_type: string;
  tests: Test[];
  patient_demographics: PatientDemographics;
  general_history: GeneralHistory;
  medical_chronology: Chronology[];
  medical_entities: Entity[];
}

export interface MedicalResultProps {
  sampleName: string;
  projectName: string | any;
  response: MedicalResponse[];
  isInsightsActive: boolean;
  isQAActive: boolean;
  handleTab: React.Dispatch<React.SetStateAction<string>>;
  insights: any;
  medicalDetails: MedicalResponse[];
  qa: any;
}
