2025-06-17 11:56:10.115 [ERROR]: Failed to update document status | {"service":"cron-job","projectId":"f64cc9ba-783f-492e-afd7-812922cea2b3","documentName":"orthopaedic_clinic.pdf","status":"Completed","httpStatus":500,"statusText":"Internal Server Error","errorBody":"<!DOCTYPE html><html lang=\"en\"><head><style data-next-hide-fouc=\"true\">body{display:none}</style><noscript data-next-hide-fouc=\"true\"><style>body{display:block}</style></noscript><meta charSet=\"utf-8\"/><meta name=\"viewport\" content=\"width=device-width\"/><meta name=\"next-head-count\" content=\"2\"/><link rel=\"shortcut icon\" href=\"/favicon.png\" type=\"image/png\"/><link rel=\"preload\" href=\"/_next/static/media/4c285fdca692ea22-s.p.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"anonymous\" data-next-font=\"size-adjust\"/><link rel=\"preload\" href=\"/_next/static/media/eafabf029ad39a43-s.p.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"anonymous\" data-next-font=\"size-adjust\"/><link rel=\"preload\" href=\"/_next/static/media/8888a3826f4a3af4-s.p.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"anonymous\" data-next-font=\"size-adjust\"/><link rel=\"preload\" href=\"/_next/static/media/b957ea75a84b6ea7-s.p.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"anonymous\" data-next-font=\"size-adjust\"/><link rel=\"preload\" href=\"/_next/static/media/0484562807a97172-s.p.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"anonymous\" data-next-font=\"size-adjust\"/><link rel=\"preload\" href=\"/_next/static/media/630c17af355fa44e-s.p.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"anonymous\" data-next-font=\"size-adjust\"/><noscript data-n-css=\"\"></noscript><script defer=\"\" nomodule=\"\" src=\"/_next/static/chunks/polyfills.js?ts=1750141569977\"></script><script src=\"/_next/static/chunks/webpack.js?ts=1750141569977\" defer=\"\"></script><script src=\"/_next/static/chunks/main.js?ts=1750141569977\" defer=\"\"></script><script src=\"/_next/static/chunks/pages/_app.js?ts=1750141569977\" defer=\"\"></script><script src=\"/_next/static/chunks/pages/_error.js?ts=1750141569977\" defer=\"\"></script><script src=\"/_next/static/development/_buildManifest.js?ts=1750141569977\" defer=\"\"></script><script src=\"/_next/static/development/_ssgManifest.js?ts=1750141569977\" defer=\"\"></script><noscript id=\"__next_css__DO_NOT_USE__\"></noscript></head><body><div id=\"__next\"></div><script src=\"/_next/static/chunks/react-refresh.js?ts=1750141569977\"></script><script id=\"__NEXT_DATA__\" type=\"application/json\">{\"props\":{\"pageProps\":{\"statusCode\":500}},\"page\":\"/_error\",\"query\":{},\"buildId\":\"development\",\"runtimeConfig\":{\"BASE_URL\":\"http://localhost:3000\"},\"isFallback\":false,\"err\":{\"name\":\"SyntaxError\",\"source\":\"server\",\"message\":\"\\\"[object Object]\\\" is not valid JSON\",\"stack\":\"SyntaxError: \\\"[object Object]\\\" is not valid JSON\\n    at JSON.parse (\\u003canonymous\\u003e)\\n    at handler (webpack-internal:///(api)/./src/pages/api/projects/documents/update-document-status.ts:13:54)\\n    at C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\next-server\\\\pages-api.runtime.dev.js:21:3039\\n    at C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\server\\\\lib\\\\trace\\\\tracer.js:131:36\\n    at NoopContextManager.with (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\@opentelemetry\\\\api\\\\index.js:1:7062)\\n    at ContextAPI.with (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\@opentelemetry\\\\api\\\\index.js:1:518)\\n    at NoopTracer.startActiveSpan (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\@opentelemetry\\\\api\\\\index.js:1:18093)\\n    at ProxyTracer.startActiveSpan (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\@opentelemetry\\\\api\\\\index.js:1:18854)\\n    at C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\server\\\\lib\\\\trace\\\\tracer.js:120:103\\n    at NoopContextManager.with (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\@opentelemetry\\\\api\\\\index.js:1:7062)\\n    at ContextAPI.with (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\@opentelemetry\\\\api\\\\index.js:1:518)\\n    at NextTracerImpl.trace (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\server\\\\lib\\\\trace\\\\tracer.js:120:28)\\n    at K (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\next-server\\\\pages-api.runtime.dev.js:21:2970)\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\\n    at async U.render (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\next-server\\\\pages-api.runtime.dev.js:21:3827)\"},\"gip\":true,\"scriptLoader\":[]}</script></body></html>"}
2025-06-17 11:56:10.126 [ERROR]: API call failed | {"service":"cron-job","requestId":"crgms","url":"http://localhost:3001/api/projects/documents/update-document-status","method":"PATCH","executionTimeMs":3775,"error":"HTTP 500: Internal Server Error","stack":"Error: HTTP 500: Internal Server Error\n    at C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:102:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async logApiCall (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\utils\\logger.js:170:22)\n    at async updateDocumentStatus (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:77:10)\n    at async C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:233:15\n    at async withPerformanceLogging (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\utils\\logger.js:137:20)\n    at async processMessagesFromQueue (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:152:10)\n    at async Task._execution (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:369:20)"}
2025-06-17 11:56:10.131 [ERROR]: Failed to process SQS message | {"service":"cron-job","messageId":"0f33fe83-ea4e-4a64-89eb-145ee0883d2b","batchNumber":1,"messageNumber":1,"processingTimeMs":3781,"error":"HTTP 500: Internal Server Error","stack":"Error: HTTP 500: Internal Server Error\n    at C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:102:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async logApiCall (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\utils\\logger.js:170:22)\n    at async updateDocumentStatus (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:77:10)\n    at async C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:233:15\n    at async withPerformanceLogging (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\utils\\logger.js:137:20)\n    at async processMessagesFromQueue (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:152:10)\n    at async Task._execution (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:369:20)","messageBody":"{\"status\": \"completed\", \"project_id\": \"f64cc9ba-783f-492e-afd7-812922cea2b3\", \"document_name\": \"orthopaedic_clinic.pdf\", \"output_path\": \"user-data/d4d82428-6011-70bf-8b22-85db5dc57722/0f62a1a0-07fc-442d-bbb3-b706865b5040/response/orthopaedic_clinic_output.json\"}"}
2025-06-17 11:59:09.189 [ERROR]: Failed to update document status | {"service":"cron-job","projectId":"0f62a1a0-07fc-442d-bbb3-b706865b5040","documentName":"orthopaedic_clinic.pdf","status":"Completed","httpStatus":500,"statusText":"Internal Server Error","errorBody":"<!DOCTYPE html><html lang=\"en\"><head><style data-next-hide-fouc=\"true\">body{display:none}</style><noscript data-next-hide-fouc=\"true\"><style>body{display:block}</style></noscript><meta charSet=\"utf-8\"/><meta name=\"viewport\" content=\"width=device-width\"/><meta name=\"next-head-count\" content=\"2\"/><link rel=\"shortcut icon\" href=\"/favicon.png\" type=\"image/png\"/><link rel=\"preload\" href=\"/_next/static/media/4c285fdca692ea22-s.p.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"anonymous\" data-next-font=\"size-adjust\"/><link rel=\"preload\" href=\"/_next/static/media/eafabf029ad39a43-s.p.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"anonymous\" data-next-font=\"size-adjust\"/><link rel=\"preload\" href=\"/_next/static/media/8888a3826f4a3af4-s.p.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"anonymous\" data-next-font=\"size-adjust\"/><link rel=\"preload\" href=\"/_next/static/media/b957ea75a84b6ea7-s.p.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"anonymous\" data-next-font=\"size-adjust\"/><link rel=\"preload\" href=\"/_next/static/media/0484562807a97172-s.p.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"anonymous\" data-next-font=\"size-adjust\"/><link rel=\"preload\" href=\"/_next/static/media/630c17af355fa44e-s.p.woff2\" as=\"font\" type=\"font/woff2\" crossorigin=\"anonymous\" data-next-font=\"size-adjust\"/><noscript data-n-css=\"\"></noscript><script defer=\"\" nomodule=\"\" src=\"/_next/static/chunks/polyfills.js?ts=1750141749180\"></script><script src=\"/_next/static/chunks/webpack.js?ts=1750141749180\" defer=\"\"></script><script src=\"/_next/static/chunks/main.js?ts=1750141749180\" defer=\"\"></script><script src=\"/_next/static/chunks/pages/_app.js?ts=1750141749180\" defer=\"\"></script><script src=\"/_next/static/chunks/pages/_error.js?ts=1750141749180\" defer=\"\"></script><script src=\"/_next/static/development/_buildManifest.js?ts=1750141749180\" defer=\"\"></script><script src=\"/_next/static/development/_ssgManifest.js?ts=1750141749180\" defer=\"\"></script><noscript id=\"__next_css__DO_NOT_USE__\"></noscript></head><body><div id=\"__next\"></div><script src=\"/_next/static/chunks/react-refresh.js?ts=1750141749180\"></script><script id=\"__NEXT_DATA__\" type=\"application/json\">{\"props\":{\"pageProps\":{\"statusCode\":500}},\"page\":\"/_error\",\"query\":{},\"buildId\":\"development\",\"runtimeConfig\":{\"BASE_URL\":\"http://localhost:3000\"},\"isFallback\":false,\"err\":{\"name\":\"SyntaxError\",\"source\":\"server\",\"message\":\"\\\"[object Object]\\\" is not valid JSON\",\"stack\":\"SyntaxError: \\\"[object Object]\\\" is not valid JSON\\n    at JSON.parse (\\u003canonymous\\u003e)\\n    at handler (webpack-internal:///(api)/./src/pages/api/projects/documents/update-document-status.ts:13:54)\\n    at C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\next-server\\\\pages-api.runtime.dev.js:21:3039\\n    at C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\server\\\\lib\\\\trace\\\\tracer.js:131:36\\n    at NoopContextManager.with (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\@opentelemetry\\\\api\\\\index.js:1:7062)\\n    at ContextAPI.with (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\@opentelemetry\\\\api\\\\index.js:1:518)\\n    at NoopTracer.startActiveSpan (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\@opentelemetry\\\\api\\\\index.js:1:18093)\\n    at ProxyTracer.startActiveSpan (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\@opentelemetry\\\\api\\\\index.js:1:18854)\\n    at C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\server\\\\lib\\\\trace\\\\tracer.js:120:103\\n    at NoopContextManager.with (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\@opentelemetry\\\\api\\\\index.js:1:7062)\\n    at ContextAPI.with (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\@opentelemetry\\\\api\\\\index.js:1:518)\\n    at NextTracerImpl.trace (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\server\\\\lib\\\\trace\\\\tracer.js:120:28)\\n    at K (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\next-server\\\\pages-api.runtime.dev.js:21:2970)\\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\\n    at async U.render (C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mrs-frontend\\\\node_modules\\\\next\\\\dist\\\\compiled\\\\next-server\\\\pages-api.runtime.dev.js:21:3827)\"},\"gip\":true,\"scriptLoader\":[]}</script></body></html>"}
2025-06-17 11:59:09.190 [ERROR]: API call failed | {"service":"cron-job","requestId":"1s615jj","url":"http://localhost:3001/api/projects/documents/update-document-status","method":"PATCH","executionTimeMs":45,"error":"HTTP 500: Internal Server Error","stack":"Error: HTTP 500: Internal Server Error\n    at C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:102:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async logApiCall (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\utils\\logger.js:170:22)\n    at async updateDocumentStatus (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:77:10)\n    at async C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:233:15\n    at async withPerformanceLogging (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\utils\\logger.js:137:20)\n    at async processMessagesFromQueue (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:152:10)\n    at async Task._execution (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:369:20)"}
2025-06-17 11:59:09.190 [ERROR]: Failed to process SQS message | {"service":"cron-job","messageId":"5dc0ac74-d644-4545-b1c7-fc0bfd788837","batchNumber":1,"messageNumber":1,"processingTimeMs":46,"error":"HTTP 500: Internal Server Error","stack":"Error: HTTP 500: Internal Server Error\n    at C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:102:13\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async logApiCall (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\utils\\logger.js:170:22)\n    at async updateDocumentStatus (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:77:10)\n    at async C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:233:15\n    at async withPerformanceLogging (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\utils\\logger.js:137:20)\n    at async processMessagesFromQueue (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:152:10)\n    at async Task._execution (C:\\Users\\<USER>\\Documents\\GitHub\\mrs-frontend\\src\\cron.js:369:20)","messageBody":"{\"status\": \"completed\", \"project_id\": \"0f62a1a0-07fc-442d-bbb3-b706865b5040\", \"document_name\": \"orthopaedic_clinic.pdf\", \"output_path\": \"user-data/d4d82428-6011-70bf-8b22-85db5dc57722/0f62a1a0-07fc-442d-bbb3-b706865b5040/response/orthopaedic_clinic_output.json\"}"}
