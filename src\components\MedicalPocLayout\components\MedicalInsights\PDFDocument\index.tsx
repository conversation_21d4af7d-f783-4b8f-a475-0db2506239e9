//@ts-nocheck
import {
  Document,
  Page,
  StyleSheet,
  Text,
  Image,
  View,
  Font,
} from "@react-pdf/renderer";
import React from "react";
import { PDFDocumentProps } from "./typings";
import { Chronology } from "../MedicalResult/typings";
import {
  breakLongWord,
  calculatePatientBMI,
  calculatePatientHWByDate,
  findDate,
  mostFrequentPatientDetail,
  removeExtraSpaces,
} from "@utils";
import { usePatientDetails } from "@hooks/usePatientDetails";

Font.register({
  family: "Raleway",
  fonts: [
    { src: "/assets/fonts/Raleway-Regular.ttf" },
    { src: "/assets/fonts/Raleway-SemiBold.ttf", fontWeight: "bold" },
    { src: "/assets/fonts/Raleway-Italic.ttf", fontStyle: "italic" },
  ],
});
Font.register({
  family: "Poppins",
  fonts: [
    { src: "/assets/fonts/Poppins-Regular.ttf" },
    { src: "/assets/fonts/Poppins-SemiBold.ttf", fontWeight: "bold" },
  ],
});

const styles = StyleSheet.create({
  page: {
    paddingHorizontal: "30px",
    paddingVertical: "20px",
    maxWidth: "100%",
  },
  underline: {
    borderBottom: "2px dashed #F05443",
    opacity: 0.9,
    width: "100%",
  },
  logoSection: {
    marginHorizontal: 10,
    marginTop: 10,
    marginBottom: 20,
    alignItems: "flex-end",
  },
  column: {
    display: "flex",
    flexDirection: "column",
    rowGap: 6,
    paddingTop: 10,
    paddingBottom: 10,
  },
  row: {
    display: "flex",
    flexDirection: "row",
  },
  section: {
    margin: 10,
    display: "flex",
    flexDirection: "column",
    alignItems: "flex-start",
  },
  title: {
    fontFamily: "Poppins",
    fontWeight: "bold",
    color: "#F05443",
    opacity: 0.9,
    fontSize: 14,
  },
  text: {
    fontFamily: "Raleway",
    fontSize: 12,
  },
  property: {
    fontFamily: "Raleway",
    fontSize: 12,
    marginRight: 10,
    fontWeight: "bold",
  },
  entityColumn: {
    display: "flex",
    alignSelf: "flex-end",
    alignItems: "center",
    width: 70,
    backgroundColor: "#F0544390",
    color: "#FFF",
    borderRadius: 5,
    fontSize: 12,
    marginVertical: 8,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  dataRow: {
    display: "flex",
    flex: "1 1 0%",
  },
  image: {
    width: "170px",
    height: "32px",
    aspectRatio: "1/1",
  },
});

const PDFDocument: React.FC<PDFDocumentProps> = ({ medicalDetails }) => {
  const patientDemographics =
    medicalDetails && medicalDetails?.map((data) => data?.patient_demographics);
  const {
    patientName,
    patientDOB,
    patientGender,
    patientAge,
    patientBMI,
    patientHeight,
    patientWeight,
    noDemographics,
  } = usePatientDetails(patientDemographics);
  const summaries =
    medicalDetails &&
    medicalDetails?.map((data) => {
      return {
        name: data?.document_name,
        summary: data?.summary?.concat("\n\n"),
      };
    });

  const medicalEntities =
    medicalDetails &&
    medicalDetails?.map((data) => {
      return {
        name: data?.document_name,
        entities: data?.medical_entities,
      };
    });

  const medicalTests =
    medicalDetails &&
    medicalDetails?.map((data) => {
      return {
        name: data?.document_name,
        tests: data?.tests,
      };
    });

  const generalHistories =
    medicalDetails &&
    medicalDetails?.map((data) => {
      return {
        document_name: data?.document_name,
        general_history: data?.general_history,
      };
    });

  const medicalChronologies =
    medicalDetails &&
    medicalDetails?.map((data) => data.medical_chronology)?.flat();

  const modifiedEntities = medicalEntities?.flatMap((doc) =>
    doc?.entities?.map((entry) => ({
      name: doc?.name,
      page_no: entry?.page_no,
      diagnosis: entry?.diagnosis,
      treatments: entry?.treatments,
      medications: entry?.medications,
    }))
  );

  const modifiedTests: any = [];
  medicalTests?.forEach((item: any) => {
    item?.tests?.forEach((it: any) => {
      modifiedTests.push({
        document_name: item.name,
        page_no: it.page_no,
        tests: item.tests,
      });
    });
  });

  const mergedChronologies =
    medicalChronologies &&
    medicalChronologies
      ?.map((item: Chronology, index: number) => {
        return {
          id: index + 1,
          date: item?.date,
          event: item?.event,
          document_name: item?.document_name,
          hospital_name: item?.hospital_name,
          doctor_name: item?.doctor_name,
          page_no: item?.page_no,
        };
      })
      ?.sort((a, b) => (new Date(b?.date) as any) - (new Date(a?.date) as any));

  const isEmptyChronologies = mergedChronologies?.length === 0;
  const isEmptyMedicalEntities = modifiedEntities?.length === 0;
  const isEmptyMedicalTests = modifiedTests?.length === 0;

  const patientDetails = {
    patient_name: patientName,
    date_of_birth: patientDOB,
    height: patientHeight,
    weight: patientWeight,
    bmi: patientBMI,
    gender: patientGender,
    age: patientAge,
  };

  const family_histories = generalHistories
    ?.map((data) => {
      return {
        document: data?.document_name,
        history: data?.general_history?.family_history,
      };
    })
    ?.reduce((acc: any, curr: any) => {
      if (
        curr?.history?.page_no !== null &&
        Object.keys(curr?.history?.values)?.length > 0
      ) {
        acc.push(curr);
      }
      return acc;
    }, []);

  const social_histories = generalHistories
    ?.map((data) => {
      return {
        document: data?.document_name,
        history: data?.general_history?.social_history,
      };
    })
    ?.reduce((acc: any, curr: any) => {
      if (
        curr?.history?.page_no !== null &&
        Object.keys(curr?.history?.values)?.length > 0
      ) {
        acc.push(curr);
      }
      return acc;
    }, []);

  const psychiatric_injuries = generalHistories
    ?.map((data) => {
      return {
        document: data?.document_name,
        history: data?.general_history?.psychiatric_injury,
      };
    })
    ?.reduce((acc: any, curr: any) => {
      if (
        curr?.history?.page_no !== null &&
        curr?.history?.values?.length > 0
      ) {
        acc.push(curr);
      }
      return acc;
    }, []);

  return (
    <Document scale={1}>
      <Page size="A4" style={styles.page}>
        <View style={styles.logoSection}>
          <Image src="/logo.png" style={styles.image} />
        </View>
        <View style={styles.section}>
          <Text style={styles.title}>Patient Demographics</Text>
          <Text style={styles.underline}></Text>
          {noDemographics ? (
            <Text
              style={[styles.text, { fontStyle: "italic", color: "#F05443" }]}
            >
              {"\n"}
              No patient demographics available.
              {"\n"} {"\n"}
            </Text>
          ) : (
            <View style={styles.column}>
              {patientDetails?.patient_name && (
                <View style={styles.row}>
                  <Text style={styles.property}>Name :</Text>
                  <Text style={styles.text}>{patientDetails.patient_name}</Text>
                </View>
              )}
              {patientDetails?.date_of_birth && (
                <View style={styles.row}>
                  <Text style={styles.property}>Date Of Birth :</Text>
                  <Text style={styles.text}>
                    {patientDetails.date_of_birth}
                  </Text>
                </View>
              )}
              {patientDetails?.gender && (
                <View style={styles.row}>
                  <Text style={styles.property}>Gender :</Text>
                  <Text style={styles.text}>{patientDetails.gender}</Text>
                </View>
              )}
              {patientDetails?.age && (
                <View style={styles.row}>
                  <Text style={styles.property}>Age :</Text>
                  <Text style={styles.text}>{patientDetails.age}</Text>
                </View>
              )}
              {patientDetails?.height && (
                <View style={styles.row}>
                  <Text style={styles.property}>Height :</Text>
                  <Text style={styles.text}>
                    {patientDetails.height} inches
                  </Text>
                </View>
              )}
              {patientDetails?.weight && (
                <View style={styles.row}>
                  <Text style={styles.property}>Weight :</Text>
                  <Text style={styles.text}>{patientDetails.weight} lbs</Text>
                </View>
              )}
              {patientDetails?.bmi && (
                <View style={styles.row}>
                  <Text style={styles.property}>BMI :</Text>
                  <Text style={styles.text}>{patientDetails.bmi}</Text>
                </View>
              )}
            </View>
          )}
        </View>
      </Page>
      <Page size="A4" style={styles.page}>
        <View style={styles.section}>
          <Text style={styles.title}>Summary</Text>
          <Text style={styles.underline}></Text>
          {summaries?.length === 0 ? (
            <Text
              style={[styles.text, { fontStyle: "italic", color: "#F05443" }]}
            >
              {"\n"}
              No summaries available.
              {"\n"} {"\n"}
            </Text>
          ) : (
            <Text
              style={[
                styles.text,
                {
                  display: "flex",
                  flexDirection: "column",
                  paddingTop: 10,
                  paddingBottom: 10,
                  rowGap: 2,
                },
              ]}
            >
              {summaries?.map(({ name, summary }, index: number) => (
                <Text
                  key={`summary-${index}`}
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    rowGap: 4,
                  }}
                >
                  <Text
                    style={{
                      display: "flex",
                    }}
                  >
                    <Text style={styles.property}>Document Name : &nbsp;</Text>
                    <Text
                      style={[
                        styles.text,
                        {
                          maxWidth: "85%",
                          wordBreak: "break-all",
                          fontWeight: "bold",
                          color: "#F05443",
                        },
                      ]}
                    >
                      {breakLongWord(removeExtraSpaces(name), 1) || "-"}
                      {"\n"} {"\n"}
                    </Text>
                  </Text>
                  <Text style={styles.text}>{summary}</Text>
                </Text>
              ))}
            </Text>
          )}
        </View>
      </Page>
      <Page size="A4" style={styles.page}>
        <View style={styles.section}>
          <Text style={styles.title}>General History</Text>
          <Text style={styles.underline}></Text>
          {family_histories && family_histories.length > 0 ? (
            <View style={styles.column}>
              <Text style={[styles.title, { color: "black", fontSize: 12 }]}>
                Family History
              </Text>
              {family_histories?.map((data: any, index: number) => (
                <React.Fragment key={`social-${index}`}>
                  <View
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      paddingTop: index !== 0 ? 10 : 0,
                    }}
                  >
                    <Text style={styles.property}>Document Name :</Text>
                    <Text
                      style={[
                        styles.text,
                        {
                          maxWidth: "85%",
                          fontWeight: "bold",
                          color: "#F05443",
                        },
                      ]}
                    >
                      {breakLongWord(removeExtraSpaces(data?.document), 1) ||
                        "-"}
                    </Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.property}>Page :</Text>
                    <Text style={[styles.text, { maxWidth: "85%" }]}>
                      {data?.history?.page_no || "-"}
                    </Text>
                  </View>
                  {Object.entries(data?.history?.values)?.map(
                    ([key, value]) => (
                      <View style={styles.row}>
                        <Text style={styles.property}>
                          {key?.charAt(0)?.toUpperCase() + key?.slice(1)} :
                        </Text>
                        <Text style={[styles.text, { maxWidth: "85%" }]}>
                          {value || "-"}
                        </Text>
                      </View>
                    )
                  )}
                </React.Fragment>
              ))}
            </View>
          ) : (
            <Text
              style={[styles.text, { fontStyle: "italic", color: "#F05443" }]}
            >
              {"\n"}
              No family history available.
              {"\n"} {"\n"}
            </Text>
          )}
          {social_histories && social_histories.length > 0 ? (
            <View style={styles.column}>
              <Text style={[styles.title, { color: "black", fontSize: 12 }]}>
                Social History
              </Text>
              {social_histories?.map((data: any, index: number) => (
                <React.Fragment key={`social-${index}`}>
                  <View
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      paddingTop: index !== 0 ? 10 : 0,
                    }}
                  >
                    <Text style={styles.property}>Document Name :</Text>
                    <Text
                      style={[
                        styles.text,
                        {
                          maxWidth: "85%",
                          fontWeight: "bold",
                          color: "#F05443",
                        },
                      ]}
                    >
                      {breakLongWord(removeExtraSpaces(data?.document), 1) ||
                        "-"}
                    </Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.property}>Page :</Text>
                    <Text style={[styles.text, { maxWidth: "85%" }]}>
                      {data?.history?.page_no || "-"}
                    </Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.property}>Smoking :</Text>
                    <Text style={[styles.text, { maxWidth: "85%" }]}>
                      {data?.history?.values?.smoking || "-"}
                    </Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.property}>Alcohol :</Text>
                    <Text style={[styles.text, { maxWidth: "85%" }]}>
                      {data?.history?.values?.alcohol || "-"}
                    </Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.property}>Tobacco :</Text>
                    <Text style={[styles.text, { maxWidth: "85%" }]}>
                      {data?.history?.values?.tobacco || "-"}
                    </Text>
                  </View>
                </React.Fragment>
              ))}
            </View>
          ) : (
            <Text
              style={[styles.text, { fontStyle: "italic", color: "#F05443" }]}
            >
              {"\n"}
              No social history available.
              {"\n"} {"\n"}
            </Text>
          )}
          {psychiatric_injuries && psychiatric_injuries.length > 0 ? (
            <View style={styles.column}>
              <Text style={[styles.title, { color: "black", fontSize: 12 }]}>
                Psychiatric Injury
              </Text>
              {psychiatric_injuries?.map((data: any, index: number) => (
                <React.Fragment key={`social-${index}`}>
                  <View
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      paddingTop: index !== 0 ? 10 : 0,
                    }}
                  >
                    <Text style={styles.property}>Document Name :</Text>
                    <Text
                      style={[
                        styles.text,
                        {
                          maxWidth: "85%",
                          fontWeight: "bold",
                          color: "#F05443",
                        },
                      ]}
                    >
                      {breakLongWord(removeExtraSpaces(data?.document), 1) ||
                        "-"}
                    </Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.property}>Page :</Text>
                    <Text style={[styles.text, { maxWidth: "85%" }]}>
                      {data?.history?.page_no || "-"}
                    </Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.property}>Injuries :</Text>
                    <Text style={[styles.text, { maxWidth: "85%" }]}>
                      {data?.history?.values?.length > 0 ? (
                        data?.history?.values?.join(", ")
                      ) : (
                        <Text>-</Text>
                      )}
                    </Text>
                  </View>
                </React.Fragment>
              ))}
            </View>
          ) : (
            <Text
              style={[styles.text, { fontStyle: "italic", color: "#F05443" }]}
            >
              {"\n"}
              No psychiatric injuries available.
              {"\n"} {"\n"}
            </Text>
          )}
        </View>
      </Page>
      <Page size="A4" style={styles.page}>
        <View style={styles.section}>
          <Text style={styles.title}>Medical Entities</Text>
          <Text style={styles.underline}></Text>
          {isEmptyMedicalEntities ? (
            <Text
              style={[styles.text, { fontStyle: "italic", color: "#F05443" }]}
            >
              {"\n"}
              No medical entities available.
              {"\n"} {"\n"}
            </Text>
          ) : (
            <View style={styles.column}>
              {modifiedEntities?.map((entity: any, index: number) => (
                <React.Fragment key={`entity-${index}`}>
                  <View
                    style={{
                      display: "flex",
                      flexDirection: "row",
                      paddingTop: index !== 0 ? 10 : 0,
                    }}
                  >
                    <Text style={styles.property}>Document Name :</Text>
                    <Text
                      style={[
                        styles.text,
                        {
                          maxWidth: "85%",
                          fontWeight: "bold",
                          color: "#F05443",
                        },
                      ]}
                    >
                      {breakLongWord(removeExtraSpaces(entity?.name), 1) || "-"}
                    </Text>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.property}>Page :</Text>
                    <Text style={[styles.text, { maxWidth: "85%" }]}>
                      {entity?.page_no || "-"}
                    </Text>
                  </View>

                  <View style={styles.row}>
                    <Text style={styles.property}>Diagnosis :</Text>
                    <View style={{ display: "flex", flexDirection: "column" }}>
                      <View>
                        <Text
                          style={{
                            color: "black",
                            fontSize: 11,
                            fontWeight: "bold",
                            opacity: 0.9,
                            fontFamily: "Raleway",
                          }}
                        >
                          Allergies
                        </Text>
                        <Text
                          style={[
                            styles.text,
                            { maxWidth: "85%", marginBottom: 10 },
                          ]}
                        >
                          {entity?.diagnosis?.allergies?.join(", ") || "-"}
                        </Text>
                      </View>
                      <View>
                        <Text
                          style={{
                            color: "black",
                            fontSize: 11,
                            fontWeight: "bold",
                            opacity: 0.9,
                            fontFamily: "Raleway",
                          }}
                        >
                          PMH
                        </Text>
                        <Text
                          style={[
                            styles.text,
                            { maxWidth: "85%", marginBottom: 10 },
                          ]}
                        >
                          {entity?.diagnosis?.pmh?.join(", ") || "-"}
                        </Text>
                      </View>
                      <View>
                        <Text
                          style={{
                            color: "black",
                            fontSize: 11,
                            fontWeight: "bold",
                            opacity: 0.9,
                            fontFamily: "Raleway",
                          }}
                        >
                          Others
                        </Text>
                        <Text
                          style={[
                            styles.text,
                            { maxWidth: "85%", marginBottom: 10 },
                          ]}
                        >
                          {entity?.diagnosis?.others?.join(", ") || "-"}
                        </Text>
                      </View>
                    </View>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.property}>Treatments :</Text>
                    <View style={{ display: "flex", flexDirection: "column" }}>
                      <View>
                        <Text
                          style={{
                            color: "black",
                            fontSize: 11,
                            fontWeight: "bold",
                            opacity: 0.9,
                            fontFamily: "Raleway",
                          }}
                        >
                          PMH
                        </Text>
                        <Text
                          style={[
                            styles.text,
                            { maxWidth: "85%", marginBottom: 10 },
                          ]}
                        >
                          {entity?.treatments?.pmh?.join(", ") || "-"}
                        </Text>
                      </View>
                      <View>
                        <Text
                          style={{
                            color: "black",
                            fontSize: 11,
                            fontWeight: "bold",
                            opacity: 0.9,
                            fontFamily: "Raleway",
                          }}
                        >
                          Others
                        </Text>
                        <Text
                          style={[
                            styles.text,
                            { maxWidth: "85%", marginBottom: 10 },
                          ]}
                        >
                          {entity?.treatments?.others?.join(", ") || "-"}
                        </Text>
                      </View>
                    </View>
                  </View>
                  <View style={styles.row}>
                    <Text style={styles.property}>Medications :</Text>
                    <View style={{ display: "flex", flexDirection: "column" }}>
                      <View>
                        <Text
                          style={{
                            color: "black",
                            fontSize: 11,
                            fontWeight: "bold",
                            opacity: 0.9,
                            fontFamily: "Raleway",
                          }}
                        >
                          PMH
                        </Text>
                        <View
                          style={{
                            display: "flex",
                            flexDirection: "column",
                            marginLeft:
                              entity?.medications?.pmh?.length > 0 ? 10 : 0,
                            marginTop:
                              entity?.medications?.pmh?.length > 0 ? 10 : 0,
                          }}
                        >
                          {entity?.medications?.pmh?.length > 0 ? (
                            entity?.medications?.pmh?.map((pmh, index) => (
                              <View
                                style={{
                                  display: "flex",
                                  flexDirection: "column",
                                }}
                                key={index}
                              >
                                <Text
                                  style={{
                                    color: "black",
                                    fontSize: 11,
                                    fontWeight: "bold",
                                    opacity: 0.9,
                                    fontFamily: "Raleway",
                                  }}
                                >
                                  Name
                                </Text>
                                <Text
                                  style={[
                                    styles.text,
                                    { maxWidth: "85%", marginBottom: 5 },
                                  ]}
                                >
                                  {pmh?.name || "-"}
                                </Text>
                                <Text
                                  style={{
                                    color: "black",
                                    fontSize: 11,
                                    fontWeight: "bold",
                                    opacity: 0.9,
                                    fontFamily: "Raleway",
                                  }}
                                >
                                  Dosage
                                </Text>
                                <Text
                                  style={[
                                    styles.text,
                                    { maxWidth: "85%", marginBottom: 10 },
                                  ]}
                                >
                                  {pmh?.dosage || "-"}
                                </Text>
                              </View>
                            ))
                          ) : (
                            <Text style={[styles.text, { marginBottom: 10 }]}>
                              -
                            </Text>
                          )}
                        </View>
                      </View>
                      <View>
                        <Text
                          style={{
                            color: "black",
                            fontSize: 11,
                            fontWeight: "bold",
                            opacity: 0.9,
                            fontFamily: "Raleway",
                          }}
                        >
                          Others
                        </Text>
                        <View
                          style={{
                            display: "flex",
                            flexDirection: "column",
                            marginLeft:
                              entity?.medications?.others?.length > 0 ? 10 : 0,
                            marginTop:
                              entity?.medications?.others?.length > 0 ? 10 : 0,
                          }}
                        >
                          {entity?.medications?.others?.length > 0 ? (
                            entity?.medications?.others?.map((other, index) => (
                              <View
                                style={{
                                  display: "flex",
                                  flexDirection: "column",
                                }}
                                key={index}
                              >
                                <Text
                                  style={{
                                    color: "black",
                                    fontSize: 11,
                                    fontWeight: "bold",
                                    opacity: 0.9,
                                    fontFamily: "Raleway",
                                  }}
                                >
                                  Name
                                </Text>
                                <Text
                                  style={[
                                    styles.text,
                                    { maxWidth: "85%", marginBottom: 5 },
                                  ]}
                                >
                                  {other.name || "-"}
                                </Text>
                                <Text
                                  style={{
                                    color: "black",
                                    fontSize: 11,
                                    fontWeight: "bold",
                                    opacity: 0.9,
                                    fontFamily: "Raleway",
                                  }}
                                >
                                  Dosage
                                </Text>
                                <Text
                                  style={[
                                    styles.text,
                                    { maxWidth: "85%", marginBottom: 10 },
                                  ]}
                                >
                                  {other?.dosage || "-"}
                                </Text>
                              </View>
                            ))
                          ) : (
                            <Text style={[styles.text, { marginBottom: 10 }]}>
                              -
                            </Text>
                          )}
                        </View>
                      </View>
                    </View>
                  </View>
                </React.Fragment>
              ))}
            </View>
          )}
        </View>
      </Page>
      <Page size="A4" style={styles.page}>
        <View style={styles.section}>
          <Text style={styles.title}>Tests</Text>
          <Text style={styles.underline}></Text>
          {isEmptyMedicalTests ? (
            <Text
              style={[styles.text, { fontStyle: "italic", color: "#F05443" }]}
            >
              {"\n"}
              No tests available.
              {"\n"} {"\n"}
            </Text>
          ) : (
            <View style={styles.column}>
              {modifiedTests?.map((currentTest: any, index: number) => {
                const filteredTests = currentTest?.tests?.find(
                  (item: any) => item?.page_no === currentTest?.page_no
                )?.tests;
                return (
                  <React.Fragment key={`test-${index}`}>
                    <View
                      style={{
                        display: "flex",
                        flexDirection: "row",
                        paddingTop: index !== 0 ? 10 : 0,
                      }}
                    >
                      <Text style={styles.property}>Document Name :</Text>
                      <Text
                        style={[
                          styles.text,
                          {
                            maxWidth: "85%",
                            fontWeight: "bold",
                            color: "#F05443",
                          },
                        ]}
                      >
                        {breakLongWord(
                          removeExtraSpaces(currentTest?.document_name),
                          1
                        ) || "-"}
                      </Text>
                    </View>
                    <View style={styles.row}>
                      <Text style={styles.property}>Page :</Text>
                      <Text style={[styles.text, { maxWidth: "85%" }]}>
                        {currentTest?.page_no || "-"}
                      </Text>
                    </View>
                    <View style={styles.row}>
                      <Text style={styles.property}>Tests :</Text>
                      <View
                        style={{
                          display: "flex",
                          flexDirection: "column",
                          rowGap: "8px",
                        }}
                      >
                        {filteredTests && filteredTests.length > 0 ? (
                          filteredTests.map((test: any, index: number) => (
                            <View
                              style={{
                                display: "flex",
                                flexDirection: "column",
                              }}
                              key={index}
                            >
                              <View style={styles.row}>
                                <Text style={styles.property}>Date :</Text>
                                <Text
                                  style={[styles.text, { maxWidth: "85%" }]}
                                >
                                  {test?.date || "-"}
                                </Text>
                              </View>
                              <View style={styles.row}>
                                <Text style={styles.property}>Name :</Text>
                                <Text
                                  style={[styles.text, { maxWidth: "85%" }]}
                                >
                                  {test?.name || "-"}
                                </Text>
                              </View>
                            </View>
                          ))
                        ) : (
                          <Text style={styles.text}>-</Text>
                        )}
                      </View>
                    </View>
                  </React.Fragment>
                );
              })}
            </View>
          )}
        </View>
      </Page>
      <Page size="A4" style={styles.page}>
        <View style={styles.section}>
          <Text style={styles.title}>Medical Chronology</Text>
          <Text style={styles.underline}></Text>
          {isEmptyChronologies ? (
            <Text
              style={[styles.text, { fontStyle: "italic", color: "#F05443" }]}
            >
              {"\n"}
              No medical chronologies available.
              {"\n"} {"\n"}
            </Text>
          ) : (
            <View style={styles.column}>
              {mergedChronologies?.map(
                (chronology: Chronology, index: number) => (
                  <React.Fragment key={`chronology-${index}`}>
                    <View
                      style={{
                        display: "flex",
                        flexDirection: "row",
                        paddingTop: index !== 0 ? 10 : 0,
                      }}
                    >
                      <Text style={styles.property}>Document Name :</Text>
                      <Text
                        style={[
                          styles.text,
                          {
                            maxWidth: "85%",
                            fontWeight: "bold",
                            color: "#F05443",
                          },
                        ]}
                      >
                        {breakLongWord(
                          removeExtraSpaces(chronology?.document_name),
                          1
                        ) || "-"}
                      </Text>
                    </View>
                    <View style={styles.row}>
                      <Text style={styles.property}>Page :</Text>
                      <Text style={[styles.text, { maxWidth: "85%" }]}>
                        {chronology?.page_no || "-"}
                      </Text>
                    </View>
                    <View style={styles.row}>
                      <Text style={styles.property}>Date :</Text>
                      <Text style={[styles.text, { maxWidth: "85%" }]}>
                        {chronology?.date || "-"}
                      </Text>
                    </View>
                    <View style={styles.row}>
                      <Text style={styles.property}>Hospital Name :</Text>
                      <Text style={[styles.text, { maxWidth: "85%" }]}>
                        {chronology?.hospital_name || "-"}
                      </Text>
                    </View>
                    <View style={styles.row}>
                      <Text style={styles.property}>Doctor Name :</Text>
                      <Text style={[styles.text, { maxWidth: "85%" }]}>
                        {chronology?.doctor_name || "-"}
                      </Text>
                    </View>
                    <View style={styles.row}>
                      <Text style={styles.property}>Event :</Text>
                      <Text style={[styles.text, { maxWidth: "85%" }]}>
                        {chronology?.event || "-"}
                      </Text>
                    </View>
                  </React.Fragment>
                )
              )}
            </View>
          )}
        </View>
      </Page>
    </Document>
  );
};

export default PDFDocument;
