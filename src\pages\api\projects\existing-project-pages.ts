import { pool } from "@utils/dbConnect";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method is not allowed" });
  }

  const { projectId } = req.query;

  try {
    const client = await pool.connect();

    const result = await client.query(
      "SELECT total_pages FROM user_projects WHERE project_id = $1",
      [projectId]
    );
    client.release();

    if (result.rows.length === 0) {
      return res.status(200).json({ success: true, pages: 0 });
    }

    const pages = result.rows[0].pages || 0;

    res.status(200).json({ success: true, pages });
  } catch (error: any) {
    console.error(error.message);
    res.status(500).json({ success: false, error: error.message });
  }
}
