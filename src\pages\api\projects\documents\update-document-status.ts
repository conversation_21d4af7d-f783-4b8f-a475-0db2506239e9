import { pool } from "@utils/dbConnect";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "PATCH") {
    return res.status(405).json({ error: "Method is not allowed" });
  }

  const { projectId, documentName, status } = JSON.parse(req.body);

  try {
    const client = await pool.connect();
    await client.query(
      "UPDATE documents SET status = $1 WHERE document_name = $2 AND project_id = $3",
      [status, documentName, projectId]
    );
    client.release();
    res.status(204).end();
  } catch (error: any) {
    console.error(error.message);
    res.status(500).json({ error: error.message });
  }
}
