{"name": "mrs-site", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev & node ./src/cron.js", "devv": "next dev", "build": "next build", "start": "next start", "start:next": "next start", "start:cron": "node ./src/cron.js", "lint": "next lint", "monitor": "node scripts/monitor-cron.js status", "monitor:health": "node scripts/monitor-cron.js health", "monitor:logs": "node scripts/monitor-cron.js logs", "monitor:errors": "node scripts/monitor-cron.js errors", "monitor:files": "node scripts/monitor-cron.js files"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.712.0", "@aws-sdk/client-s3": "^3.569.0", "@aws-sdk/client-sqs": "^3.629.0", "@fortawesome/fontawesome-free": "^6.5.1", "@react-pdf/renderer": "^3.4.0", "@types/formidable": "^3.4.5", "@types/lodash": "^4.17.13", "@types/pg": "^8.11.2", "@types/qrcode": "^1.5.5", "@types/uuid": "^9.0.8", "aws-amplify": "^6.3.2", "dompurify": "^3.1.7", "dotenv": "^16.4.5", "formidable": "^3.5.1", "formik": "^2.4.5", "framer-motion": "^10.16.16", "hover.css": "^2.3.2", "lodash": "^4.17.21", "next": "14.0.3", "node-cron": "^3.0.3", "node-fetch": "^2", "pdfjs-dist": "^4.0.379", "pg": "^8.11.3", "qrcode": "^1.5.4", "react": "^18", "react-dom": "^18", "react-pdf": "^7.7.1", "sharp": "^0.34.2", "uuid": "^9.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "yup": "^1.3.2"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@types/dompurify": "^3.0.5", "@types/fs-extra": "^11.0.4", "@types/node": "^20", "@types/node-cron": "^3.0.11", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "classnames": "^2.3.2", "eslint": "^8", "eslint-config-next": "14.0.3", "postcss": "^8", "sass": "^1.69.5", "tailwindcss": "^3.3.0", "typescript": "^5"}}