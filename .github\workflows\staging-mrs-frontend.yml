name: Staging
on:
  push:
    branches: [ staging ]
  workflow_dispatch:

env:
  ECR_REPOSITORY: staging/mrs-frontend
  EKS_CLUSTER_NAME: demo-site 
  AWS_REGION: ap-south-1

jobs:
  
  build-service:
    runs-on: ubuntu-latest

    env:
      NEXT_PUBLIC_MAX_PROJECTS: ${{ secrets.NEXT_PUBLIC_MAX_PROJECTS }}
      NEXT_PUBLIC_API_DOMAIN: ${{ secrets.NEXT_PUBLIC_API_DOMAIN }}
      #NEXT_PUBLIC_QNA_API_DOMAIN: ${{ secrets.NEXT_PUBLIC_QNA_API_DOMAIN }}
      NEXT_PUBLIC_MAX_MEDICAL_FILE_SIZE: ${{ secrets.NEXT_PUBLIC_MAX_MEDICAL_FILE_SIZE }}
      NEXT_PUBLIC_MAX_PAGES_LIMIT: ${{ secrets.NEXT_PUBLIC_MAX_PAGES_LIMIT }}
      NEXT_PUBLIC_DB_USER: ${{ secrets.NEXT_PUBLIC_DB_USER }}
      NEXT_PUBLIC_DB_HOST: ${{ secrets.NEXT_PUBLIC_DB_HOST }}
      NEXT_PUBLIC_DB_PORT: ${{ secrets.NEXT_PUBLIC_DB_PORT }}
      NEXT_PUBLIC_DB_NAME: ${{ secrets.NEXT_PUBLIC_DB_NAME }}
      NEXT_PUBLIC_DB_PASSWORD: ${{ secrets.NEXT_PUBLIC_DB_PASSWORD }}
      NEXT_PUBLIC_AWS_REGION: ${{ secrets.NEXT_PUBLIC_AWS_REGION }}
      NEXT_PUBLIC_AWS_PROJECT_REGION: ${{ secrets.NEXT_PUBLIC_AWS_PROJECT_REGION }}
      NEXT_PUBLIC_AWS_COGNITO_REGION: ${{ secrets.NEXT_PUBLIC_AWS_COGNITO_REGION }}
      NEXT_PUBLIC_AWS_COGNITO_IDENTITY_POOL_ID: ${{ secrets.NEXT_PUBLIC_AWS_COGNITO_IDENTITY_POOL_ID }}
      NEXT_PUBLIC_AWS_USER_POOLS_ID: ${{ secrets.NEXT_PUBLIC_AWS_USER_POOLS_ID }}
      NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: ${{ secrets.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID }}
      NEXT_PUBLIC_SQS_PUBLISH_QUEUE_URL: ${{ secrets.NEXT_PUBLIC_SQS_PUBLISH_QUEUE_URL }}
      NEXT_PUBLIC_SQS_CONSUME_QUEUE_URL: ${{ secrets.NEXT_PUBLIC_SQS_CONSUME_QUEUE_URL }}

    steps:

    - name: Set short git commit SHA
      id: commit
      uses: prompt/actions-commit-hash@v2

    - name: Check out code
      uses: actions/checkout@v2

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{env.AWS_REGION}}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Export Environment Variables
      run: |
        # Export all environment variables to a build args file
        printenv | grep '^NEXT_PUBLIC_' | sed 's/^\([^=]*=\)\(.*\)$/\1"\2"/' > .env

    - name: Build, tag, and push image to Amazon ECR
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ steps.commit.outputs.short }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -f Dockerfile .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

    - name: Update kube config
      run: aws eks update-kubeconfig --name $EKS_CLUSTER_NAME --region $AWS_REGION

    - name: Test Kube Configs
      run: kubectl version

    - name: Deploy to EKS
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ steps.commit.outputs.short }}
      run: |
        echo "Deploying to EKS..."
        sed -i "s|DOCKER_IMAGE|${ECR_REGISTRY}/${ECR_REPOSITORY}:${IMAGE_TAG}|g" .k8s/mrs-frontend-deployment.yaml
        kubectl apply -f .k8s/mrs-frontend-deployment.yaml
