import React, { useCallback } from "react";

import { Loader } from "@components";
import Head from "next/head";
import { Stage, useAuthState } from "@contexts/AuthContext";

import LoginLayout from "@components/Layout/LoginLayout";
import { LOGIN_STAGE, SETUP_TOTP_STAGE, VERIFY_TOTP_STAGE } from "@constants";
import Login from "./Login";
import SetupTotp from "./SetupTOTP";
import VerifyTotp from "./VerifyTOTP";

const LoginPage: React.FC = () => {
  const { stage, loading } = useAuthState();

  const showAuthStage = useCallback(
    (currentStage: Stage) => {
      switch (currentStage) {
        case LOGIN_STAGE:
          return <Login />;
        case SETUP_TOTP_STAGE:
          return <SetupTotp />;
        case VERIFY_TOTP_STAGE:
          return <VerifyTotp />;
        default:
          return <Login />;
      }
    },
    [stage]
  );

  if (loading) {
    return <Loader />;
  } else {
    return (
      <>
        <Head>
          <title>Login | Maruti Techlabs</title>
        </Head>
        <LoginLayout>{showAuthStage(stage)}</LoginLayout>
      </>
    );
  }
};

export default LoginPage;
