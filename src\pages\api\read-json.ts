import type { NextApiRequest, NextApiResponse } from "next";
import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import { Readable } from "stream";
import { awsValues } from "@constants/aws";

const s3Client = new S3Client({ region: awsValues.region });

const getJsonFromS3 = async (bucket: string, key: string) => {
  const command = new GetObjectCommand({
    Bucket: bucket,
    Key: key,
  });
  const data = await s3Client.send(command);
  const stream = data.Body as Readable;
  const chunks: Buffer[] = [];

  for await (const chunk of stream) {
    chunks.push(chunk);
  }
  return JSON.parse(Buffer.concat(chunks as any).toString("utf-8"));
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { bucket, key } = req.query;

  if (!bucket || !key) {
    return res
      .status(400)
      .json({ success: false, message: "Bucket and key are required" });
  }

  try {
    const jsonData = await getJsonFromS3(bucket as string, key as string);
    return res.status(200).json({ success: true, data: [jsonData] });
  } catch (error) {
    console.error("Error fetching from S3:", error);
    return res
      .status(500)
      .json({ success: false, error: (error as Error).message });
  }
}
