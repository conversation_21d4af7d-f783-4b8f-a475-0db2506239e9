@tailwind base;
@tailwind components;
@tailwind utilities;

@import "hover.css/css/hover-min.css";
@import "@components/styles.css";
@import "@pages/styles.css";

html {
  scroll-behavior: smooth;
}
.check-icon {
  @apply text-lime-600 text-xl;
}
.cross-icon {
  @apply text-brand-secondary/80 text-xl;
}
.suggestion {
  @apply flex-shrink-0 border border-brand-secondary/50 rounded-lg py-2 px-3 cursor-pointer hover:bg-brand-secondary/80 hover:text-white transition duration-150 disabled:cursor-default disabled:border-gray-400 disabled:hover:bg-transparent disabled:text-gray-400;
}
.qa-height {
  height: 725px !important;
}
.query-loader {
  width: 40px;
  aspect-ratio: 4;
  --_g: no-repeat radial-gradient(circle closest-side, #f05443 90%, #0000);
  background: var(--_g) 0% 50%, var(--_g) 50% 50%, var(--_g) 100% 50%;
  background-size: calc(100% / 3) 100%;
  animation: l7 1s infinite linear;
}
@keyframes l7 {
  33% {
    background-size: calc(100% / 3) 0%, calc(100% / 3) 100%, calc(100% / 3) 100%;
  }
  50% {
    background-size: calc(100% / 3) 100%, calc(100% / 3) 0%, calc(100% / 3) 100%;
  }
  66% {
    background-size: calc(100% / 3) 100%, calc(100% / 3) 100%, calc(100% / 3) 0%;
  }
}
.latest-animation:before {
  animation: highlight 1s linear infinite;
}
@keyframes highlight {
  0% {
    opacity: 0;
    left: -15px;
  }
  20% {
    opacity: 0.3;
    left: 0;
  }
  30% {
    opacity: 0.45;
    left: 5px;
  }
  50% {
    opacity: 0.45;
    left: 10px;
  }
  80% {
    opacity: 0.45;
    left: 20px;
  }
  100% {
    opacity: 0;
    left: 35px;
  }
}
.upload-icon svg {
  fill: #f0544390;
}
.c-scroll::-webkit-scrollbar {
  width: 9px !important;
  height: 10px !important;
  transition: all 0.3s ease;
  border-radius: 8px !important;
}
.c-scroll::-webkit-scrollbar:hover {
  background: #efefef;
}
.c-scroll:hover::-webkit-scrollbar {
  transition: all 0.3s ease;
}
.c-scroll::-webkit-scrollbar-track {
  background-color: transparent !important;
}
.c-scroll::-webkit-scrollbar-thumb {
  border-radius: 8px !important;
  border: 2px solid white !important;
  background-color: #cacaca !important;
}
.pdf-dimensions {
  display: flex;
  justify-content: center;
  width: calc(100vw - 150px) !important;
  height: calc(100vh - 150px) !important;
  max-width: 700px !important;
  max-height: 800px !important;
}
.upload-icon svg path:nth-child(3) {
  transform: translate(-40px, -30px) !important;
  rotate: 180deg !important;
}
input[type="number"] {
  -moz-appearance: textfield;
  -webkit-appearance: none;
  appearance: none;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
@media only screen and (max-width: 720px) {
  .pdf-dimensions {
    justify-content: flex-start;
  }
}
.d-width {
  width: 90vw;
}

@media only screen and (max-width: 1440px) {
  .d-width {
    width: 90vw;
  }
}

@media only screen and (max-width: 768px) {
  .d-width {
    width: 95vw;
    font-size: 0.9rem;
  }
  .tp-pos {
    top: 10rem;
  }
}
@media only screen and (min-width: 2560px) {
  .d-width {
    width: 70vw;
    font-size: 1.1rem;
  }
}
