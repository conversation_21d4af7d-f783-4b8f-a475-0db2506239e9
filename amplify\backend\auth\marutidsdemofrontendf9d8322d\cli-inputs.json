{"version": "1", "cognitoConfig": {"identityPoolName": "marutidsdemofrontendf9d8322d_identitypool_f9d8322d", "allowUnauthenticatedIdentities": false, "resourceNameTruncated": "marutif9d8322d", "userPoolName": "marutidsdemofrontendf9d8322d_userpool_f9d8322d", "autoVerifiedAttributes": ["email"], "mfaConfiguration": "OFF", "mfaTypes": ["SMS Text Message"], "smsAuthenticationMessage": "Your authentication code is {####}", "smsVerificationMessage": "Your verification code is {####}", "emailVerificationSubject": "Your verification code", "emailVerificationMessage": "Your verification code is {####}", "defaultPasswordPolicy": false, "passwordPolicyMinLength": 8, "passwordPolicyCharacters": [], "requiredAttributes": ["email"], "aliasAttributes": [], "userpoolClientGenerateSecret": false, "userpoolClientRefreshTokenValidity": 30, "userpoolClientWriteAttributes": ["email"], "userpoolClientReadAttributes": ["email"], "userpoolClientLambdaRole": "marutif9d8322d_userpoolclient_lambda_role", "userpoolClientSetAttributes": false, "sharedId": "f9d8322d", "resourceName": "marutidsdemofrontendf9d8322d", "authSelections": "identityPoolAndUserPool", "useDefault": "default", "usernameAttributes": ["email"], "userPoolGroupList": [], "serviceName": "Cognito", "usernameCaseSensitive": false, "useEnabledMfas": true}}