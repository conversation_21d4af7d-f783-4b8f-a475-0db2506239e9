import { Project, Sample } from "@typings";

export interface QAResponse {
  query: string;
  result: string;
}
export interface QueryState {
  id: number;
  query: string;
  result: string;
}
export interface QAProps {
  isSample: boolean;
  sample: Sample;
  project: Project;
  resultQueries: QueryState[];
  addQueryResult: (queryObj: QAResponse) => void;
  handleResultQueries: React.Dispatch<React.SetStateAction<QueryState[]>>;
  counter: number;
  handleCounter: () => void;
  queryLoader: boolean;
  handleQueryLoader: (status: boolean) => void;
  containerRef: React.RefObject<HTMLDivElement>;
  handleQAModal: React.Dispatch<React.SetStateAction<boolean>>;
  handleQAError: React.Dispatch<
    React.SetStateAction<{ error: boolean; message: string }>
  >;
  resetCounter: () => void;
}
