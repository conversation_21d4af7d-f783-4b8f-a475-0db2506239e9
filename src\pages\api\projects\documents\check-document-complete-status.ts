import { pool } from "@utils/dbConnect";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method is not allowed" });
  }

  const { projectId } = req.query;

  if (!projectId) {
    return res.status(400).json({ error: "projectId is required" });
  }

  try {
    const client = await pool.connect();

    const result = await client.query(
      "SELECT COUNT(*) FROM documents WHERE project_id = $1 AND (status IS NULL OR status != 'Completed')",
      [projectId]
    );

    client.release();

    const incompleteCount = parseInt(result.rows[0].count, 10);
    return res.status(200).json({
      allCompleted: incompleteCount === 0,
    });
  } catch (error: any) {
    console.error("Database query error:", error.message);
    return res.status(500).json({
      allCompleted: false,
      error: error.message,
    });
  }
}
