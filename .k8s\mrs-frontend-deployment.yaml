apiVersion: apps/v1
kind: Deployment
metadata: 
  name: mrs-frontend
  namespace: staging
  labels:
    app: mrs-frontend

spec:
  replicas: 1
  selector:
    matchLabels:
      app: mrs-frontend
  
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%

  template:
    metadata:
      name: mrs-frontend
      labels:
        app: mrs-frontend
    
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: staging
                operator: In
                values:
                - "true"
      containers:
      - name: mrs-frontend
        image: DOCKER_IMAGE
        # volumeMounts:
        # - name: mrs-file
        #   mountPath: /app/.env
        #   subPath: .env
        # envFrom:
        # - configMapRef:
        #     name: frontend-config
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: mrs-frontend
        resources:
          requests:
            memory: "300Mi"
            cpu: "256m"
          limits:
            memory: "600Mi"
            cpu: "300m"
      # volumes:
      # - name: mrs-file
      #   configMap:
      #     name: frontend-config
