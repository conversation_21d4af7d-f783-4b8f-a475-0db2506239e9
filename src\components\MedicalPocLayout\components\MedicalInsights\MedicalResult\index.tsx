import { Raleway } from "next/font/google";
import { motion } from "framer-motion";
import cx from "classnames";
import React from "react";
import { tabs } from "@constants/tabs";
import { MedicalResultProps } from "./typings";
import PDFDocument from "../PDFDocument";
import { PDFDownloadLink } from "@react-pdf/renderer";

const raleway = Raleway({
  display: "swap",
  weight: ["400", "500", "600"],
  subsets: ["latin"],
});
const activeInsights =
  "bg-brand-secondary/80 rounded-tl-full rounded-bl-full text-white";
const activeQA =
  "bg-brand-secondary/80 rounded-tr-full rounded-br-full text-white";
const MedicalResult: React.FC<MedicalResultProps> = ({
  isInsightsActive,
  isQAActive,
  handleTab,
  medicalDetails,
  sampleName,
  projectName,
  insights,
  qa,
}) => {
  return (
    <div className="bshadow rounded-xl w-full max-h-[900px] overflow-y-auto c-scroll">
      <div
        className={`flex w-full py-6 px-8 justify-between items-center bg-white text-black text-[22px] rounded-tl-xl rounded-tr-xl`}
      >
        <div className="text-3xl text-brand-secondary/80 font-bold">
          Medical Insights
        </div>
        <div className="flex">
          <PDFDownloadLink
            document={<PDFDocument medicalDetails={medicalDetails} />}
            fileName={!!projectName ? `${projectName}.pdf` : sampleName}
            className="group flex gap-x-2 items-center bg-brand-secondary/80 text-base text-white p-3 rounded-full mr-3 hover:bg-opacity-90 transform duration-200 cursor-pointer"
            title="Download PDF"
          >
            <i className="fas fa-arrow-circle-down group-hover:translate-y-0.5 transform duration-200 text-2xl"></i>
          </PDFDownloadLink>
          <div
            className={cx(
              "inline-flex text-base items-center border border-brand-secondary rounded-full text-black"
            )}
          >
            <motion.span
              className={cx("p-3 cursor-pointer", {
                [activeInsights]: isInsightsActive,
              })}
              onClick={() => handleTab(tabs.insights)}
              whileTap={{ x: -1 }}
            >
              Insights
            </motion.span>
            <motion.span
              className={cx("p-3 cursor-pointer", {
                [activeQA]: isQAActive,
              })}
              onClick={() => handleTab(tabs.qa)}
              whileTap={{ x: 1 }}
            >
              Q&A
            </motion.span>
          </div>
        </div>
      </div>
      {isInsightsActive ? insights : null}
      {isQAActive ? qa : null}
    </div>
  );
};

export default MedicalResult;
