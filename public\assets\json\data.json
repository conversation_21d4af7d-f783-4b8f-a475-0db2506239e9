{"data": [{"id": 1, "name": "MI", "src": "/assets/images/Medical Insights Extractor.png", "title": "Medical Insights Extractor", "subtitle": "Automated Medical Record analysis system to extract relevant information from medical records and allow user to get answers with a simple chat.", "samples": [{"sampleInd": 1, "fileName": "orthopaedic_clinic.pdf", "title": "Orthopaedic Clinic", "path": "/assets/images/orthopaedic_clinic.png", "jsonPath": "sample-data/sample1_v1.1/response/orthopaedic_clinic_output.json"}, {"sampleInd": 2, "fileName": "pain_management.pdf", "title": "Pain Management", "path": "/assets/images/pain_management.png", "jsonPath": "sample-data/sample2_v1.1/response/pain_management_output.json"}, {"sampleInd": 3, "fileName": "orthopaedic_clinic_visit.pdf", "title": "Orthopaedic Clinic Visit", "path": "/assets/images/orthopaedic_clinic_visit.png", "jsonPath": "sample-data/sample3_v1.1/response/orthopaedic_clinic_visit_output.json"}]}]}