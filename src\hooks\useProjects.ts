import { FormikValues } from "formik";
import { useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { Project } from "@typings";
import { retrieveProject } from "@utils/dbActions";
import { statuses } from "@constants/db";
import { SOMETHING_WENT_WRONG, UNIQUE_PROJECT_NAME } from "@constants";
import { useAuthState } from "@contexts/AuthContext";
import { fetchProjectResponseFromS3API } from "@utils/medicalApi";
import { buckets } from "@constants/buckets";
import { fetchCsrfToken } from "@utils";

export const useProjects = (
  handleProjectErrorModal: any,
  handleProjectError: any,
  handleDetails: any,
  projectStatus: any,
  handleProjectStatus: any,
  setUploadLoader: any
) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [projectLoading, setProjectLoading] = useState<boolean>(false);
  const { user } = useAuthState();
  const [selectedProject, setSelectedProject] = useState<Project>({
    project_id: -1,
    project_name: "",
  });

  const setTheSelectedProject = (project: Project) => {
    setSelectedProject(project);
  };

  const handleSelectedProject = async (
    project: Project,
    handleUploadLoader: React.Dispatch<React.SetStateAction<boolean>>
  ) => {
    setSelectedProject(project);
    if (window && typeof window !== undefined) {
      window.localStorage.setItem("currentProject", JSON.stringify(project));
      const isCurrentSampleExists = JSON.parse(
        window.localStorage.getItem("currentSample")!
      );
      if (isCurrentSampleExists && isCurrentSampleExists !== null)
        window.localStorage.removeItem("currentSample");
    }
    if (project?.project_id !== selectedProject?.project_id) {
      try {
        handleUploadLoader(true);
        const { project_id } = project;
        const projectResponse = await retrieveProject(project_id);
        if (projectResponse.ok) {
          const projectStatus = await projectResponse.json();
          handleProjectStatus(projectStatus?.status);
        }
      } catch (error) {
        console.error(error);
      } finally {
        handleUploadLoader(false);
      }
    }
  };

  const fetchUserProjects = async () => {
    try {
      handleProjectError({ error: false, message: "" });
      setProjectLoading(true);
      const userId = localStorage.getItem("userId") || user?.userId;
      const response = await fetch(`/api/projects?userId=${userId}`);
      if (response.ok) {
        const projects = await response.json();
        setProjects(projects);
      } else {
        const errorResponse = await response.json();
        throw new Error(errorResponse.error);
      }
    } catch (error: any) {
      console.error(error.message);
      handleProjectErrorModal(true);
      handleProjectError({ error: true, message: SOMETHING_WENT_WRONG });
    } finally {
      setProjectLoading(false);
    }
  };

  const handleProjectSubmission = async (values: FormikValues) => {
    try {
      handleProjectError({ error: false, message: "" });
      setProjectLoading(true);
      const status = statuses.Created;
      const totalPages = 0;
      const { projectName } = values;
      const userId = localStorage.getItem("userId") || user.userId;
      const projectId = uuidv4();

      const csrfToken = await fetchCsrfToken();
      const response = await fetch("/api/projects/create-project", {
        method: "POST",
        headers: {
          "X-CSRF-Token": csrfToken,
        },
        body: JSON.stringify({
          userId,
          projectId,
          projectName,
          totalPages,
          status,
        }),
      });
      if (response.ok) {
        handleProjectStatus(statuses.Created);
        setSelectedProject({
          project_id: projectId,
          project_name: projectName,
        });
        if (window && typeof window !== undefined) {
          window.localStorage.setItem(
            "currentProject",
            JSON.stringify({ project_id: projectId, project_name: projectName })
          );
        }
        await fetchUserProjects();
      } else {
        const errorResponse = await response.json();
        throw new Error(errorResponse.error);
      }
    } catch (error: any) {
      console.error(error.message);
      const message = error?.message?.includes("duplicate")
        ? UNIQUE_PROJECT_NAME
        : SOMETHING_WENT_WRONG;
      handleProjectErrorModal(true);
      handleProjectError({ error: true, message });
    } finally {
      setProjectLoading(false);
    }
  };

  useEffect(() => {
    fetchUserProjects();
  }, []);

  useEffect(() => {
    const fetchProject = async () => {
      try {
        const response = await retrieveProject(selectedProject?.project_id);
        if (response.ok) {
          const retrievedProject = await response.json();
          handleProjectStatus(retrievedProject?.status);
        }
      } catch (error) {
        console.error("Error fetching project status:", error);
      }
    };

    const shouldFetchProject = () => {
      return (
        projects.length > 0 &&
        selectedProject?.project_id !== -1 &&
        projectStatus === statuses.Processing
      );
    };

    if (shouldFetchProject()) {
      fetchProject();
    }

    const interval = setInterval(() => {
      if (shouldFetchProject()) {
        fetchProject();
      }
    }, 10000);

    return () => clearInterval(interval);
  }, [projects, selectedProject?.project_id, projectStatus]);

  useEffect(() => {
    const updateMedicalResult = async () => {
      const userId = localStorage.getItem("userId") || user?.userId;
      setUploadLoader(true);
      const jsonData = await fetchProjectResponseFromS3API(
        buckets.mi,
        userId,
        selectedProject?.project_id
      );
      setUploadLoader(false);
      handleDetails(jsonData);
    };
    if (
      projectStatus === statuses.Completed &&
      selectedProject?.project_id !== -1
    )
      updateMedicalResult();
  }, [projectStatus, selectedProject?.project_id]);

  return {
    selectedProject,
    setTheSelectedProject,
    handleSelectedProject,
    projects,
    handleProjectSubmission,
    projectLoading,
  };
};
