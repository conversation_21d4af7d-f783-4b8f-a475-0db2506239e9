import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import { awsValues } from "@constants/aws";
import { buckets } from "@constants/buckets";
import { NextApiRequest, NextApiResponse } from "next";
import { Readable } from "stream";

const s3 = new S3Client({
  region: awsValues.region,
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { userId, id, documentName } = req.query;
  const bucketName = buckets.mi;
  const objectKey = `user-data/${userId}/${id}/request/${documentName}`;
  try {
    const params = {
      Bucket: bucketName,
      Key: objectKey,
    };
    const { Body } = await s3.send(new GetObjectCommand(params));

    if (Body instanceof Readable) {
      const chunks: Uint8Array[] = [];
      for await (const chunk of Body) {
        chunks.push(chunk);
      }
      const buffer = Buffer.concat(chunks);

      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `inline; filename="${documentName}"`
      );
      res.status(200).send(buffer);
    } else {
      res.status(500).json({ error: "Failed to read PDF from S3" });
    }
  } catch (error) {
    console.error("Error reading PDF:", error);
    res.status(500).json({ error: "Error fetching PDF from S3" });
  }
}
