import React, { useEffect } from "react";
import { motion } from "framer-motion";
import { Raleway } from "next/font/google";
import { useFormik } from "formik";
import * as Yup from "yup";
import cx from "classnames";
import { MAX_TOTP_LENGTH } from "@constants";
import LoginLayout from "@components/Layout/LoginLayout";
import { useAuthState } from "@contexts/AuthContext";
import { Loader } from "@components";
import Link from "next/link";

const raleway = Raleway({
  display: "swap",
  weight: ["500"],
  subsets: ["latin"],
});

const VerifyTotp: React.FC = () => {
  const { verifyTotp, processLoader, error, setError } = useAuthState();
  const {
    handleSubmit,
    values,
    errors,
    setErrors,
    setTouched,
    setFieldValue,
    resetForm,
    touched,
  } = useFormik({
    initialValues: {
      totp: "",
    },
    validationSchema: Yup.object({
      totp: Yup.string()
        .matches(/^\d*$/, "Only numeric values are allowed")
        .min(MAX_TOTP_LENGTH, `TOTP code should be ${MAX_TOTP_LENGTH} digits`)
        .max(MAX_TOTP_LENGTH, `TOTP code should be ${MAX_TOTP_LENGTH} digits`)
        .required("Please enter TOTP code"),
    }),
    onSubmit: (values) => {
      verifyTotp(values.totp).then(() => resetForm());
    },
  });

  const handleTotpChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    const sanitizedValue = value.replace(/[^0-9]/g, "");
    if (sanitizedValue.length <= MAX_TOTP_LENGTH) {
      setFieldValue("totp", sanitizedValue);
    }
  };

  useEffect(() => {
    if (error) setError("");
    setErrors({});
    setTouched({});
  }, []);

  return (
    <div className="flex flex-col items-start md:items-center justify-center bg-brand-white w-full md:w-[600px] px-16 md:px-8">
      <motion.div className="relative flex flex-col space-y-1 w-full md:w-[400px]">
        <motion.h1
          className="gradient-text"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          Verify TOTP
        </motion.h1>
        <motion.span
          className={`${raleway.className} text-sm font-medium`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          Enter TOTP code from your authenticator app:
        </motion.span>
        <motion.form onSubmit={handleSubmit} className="pt-8 pb-4">
          <motion.div
            className="relative flex flex-col space-y-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <div className="flex items-center space-x-3">
              <label htmlFor="totp" className="text-base font-medium">
                Enter TOTP code
              </label>
              {errors.totp && touched.totp && (
                <motion.span
                  className="absolute left-32 text-sm text-brand-secondary max-w-[220px]"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                >
                  {errors.totp}
                </motion.span>
              )}
            </div>
            <motion.input
              id="totp"
              type="text"
              autoFocus
              value={values.totp}
              onChange={handleTotpChange}
              className={cx(
                "bg-white p-[10px] rounded-[3px] text-base focus:outline-brand-secondary border !appearance-none",
                {
                  "border-brand-secondary": errors.totp && touched.totp,
                }
              )}
              placeholder="Enter TOTP code"
            />
          </motion.div>
          <motion.button
            type="submit"
            className="flex justify-center items-center px-[35px] w-full mt-8 py-[13px] text-white border border-brand-secondary bg-transparent rounded-[3px] font-semibold text-base btn-animate transform duration-300 overflow-hidden z-10 hover:text-brand-secondary focus:outline-none"
            onClick={() => setError("")}
            initial={{ opacity: 0, transitionDuration: "0.1s" }}
            animate={{ opacity: 1, transitionDuration: "0.1s" }}
          >
            {processLoader ? <Loader classNames="sm" /> : "Verify TOTP"}
          </motion.button>
        </motion.form>
        <motion.div className="relative">
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            className={cx(
              "absolute text-brand-secondary bg-brand-secondary/10 px-4 py-1 rounded-md w-full",
              {
                hidden: !error,
                "flex items-center": !!error,
              }
            )}
          >
            <i className="fas fa-warning mr-2"></i>
            <span>{error}</span>
          </motion.div>
        </motion.div>
      </motion.div>
      <div className="relative flex flex-col py-2 border px-5 border-brand-warning/20 rounded-md w-full md:w-[400px] top-80 tp-pos">
        <i className="absolute -top-2 -left-2 fas fa-question-circle text-brand-warning text-xl"></i>
        <span className="text-xl text-brand-warning font-semibold mb-1">
          Having Trouble?
        </span>
        <hr className="border-t border-t-brand-warning/20 pb-2" />
        <span className="text-sm text-black/70">
          If you've lost access to your authenticator app, please contact our
          support team immediately to regain access to your account securely.
        </span>
      </div>
    </div>
  );
};

export default VerifyTotp;
