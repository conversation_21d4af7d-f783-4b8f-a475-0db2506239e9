import React from "react";
import { motion } from "framer-motion";
import Loader from "..";

const BookLoader: React.FC = () => {
  return (
    <motion.div
      className="w-full h-full top-1/2 -translate-y-1/2 fixed bg-black bg-opacity-70 z-20"
      initial={{
        opacity: 0,
      }}
      animate={{
        opacity: 1,
      }}
    >
      <motion.div
        className="flex items-center justify-center h-full"
        initial={{
          y: -100,
        }}
        animate={{ y: 0 }}
        transition={{ duration: 0.2 }}
      >
        <Loader isAlt />
      </motion.div>
    </motion.div>
  );
};

export default BookLoader;
