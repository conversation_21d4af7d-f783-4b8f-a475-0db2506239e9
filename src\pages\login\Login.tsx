import { useAuthState } from "@contexts/AuthContext";
import { <PERSON><PERSON><PERSON>, Raleway } from "next/font/google";
import { useFormik } from "formik";
import { motion } from "framer-motion";
import React, { useEffect, useRef, useState } from "react";
import * as Yup from "yup";
import cx from "classnames";
import {
  EMAIL_REGEX,
  EMPTY_EMAIL,
  EMPTY_PASSWORD,
  INVALID_EMAIL,
  INVALID_PASSWORD,
} from "@constants";
import { Loader } from "@components";

const raleway = Raleway({
  display: "swap",
  weight: ["500"],
  subsets: ["latin"],
});

const poppins = Poppins({
  display: "swap",
  weight: ["600"],
  subsets: ["latin"],
});

interface LoginForm {
  email: string;
  password: string;
}

const Login = () => {
  const fieldRef = useRef<HTMLInputElement>(null);
  const { error, setError, loginUser, processLoader, setProcessLoader } =
    useAuthState();

  const [isPassVisible, setIsPassVisible] = useState(false);

  const handlePasswordVisibility = () => {
    setIsPassVisible((prev) => !prev);
  };

  const handleLogin = async (formValues: any) => {
    const { email, password } = formValues;
    setError("");
    setProcessLoader(true);
    await loginUser(email, password);
    setProcessLoader(false);
  };

  const { values, errors, touched, handleSubmit, handleChange } =
    useFormik<LoginForm>({
      initialValues: {
        email: "",
        password: "",
      },
      validationSchema: Yup.object({
        email: Yup.string()
          .required(EMPTY_EMAIL)
          .matches(EMAIL_REGEX, INVALID_EMAIL),
        password: Yup.string()
          .min(8, INVALID_PASSWORD)
          .required(EMPTY_PASSWORD),
      }),
      onSubmit: (values: LoginForm) => {
        handleLogin(values);
      },
    });

  useEffect(() => {
    if (error) setError("");
    if (fieldRef.current) {
      fieldRef.current.focus();
    }
  }, []);

  return (
    <div className="flex flex-col items-start md:items-center space-y-8 justify-center bg-brand-white w-full md:w-[600px] px-16 md:px-8">
      <motion.div className="relative flex flex-col space-y-1 w-full md:w-[350px]">
        <motion.h1
          className="gradient-text"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          Welcome
        </motion.h1>
        <motion.span
          className={`${raleway.className} text-sm font-medium`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          Please login to your account
        </motion.span>
        <motion.form
          onSubmit={handleSubmit}
          className={`${raleway.className} flex flex-col space-y-7 py-6`}
        >
          <motion.div
            className="relative flex flex-col space-y-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <motion.div className="flex items-center space-x-3">
              <motion.label
                htmlFor="email"
                className={`${poppins.className} text-base font-bold`}
              >
                Email
              </motion.label>
              {errors.email && touched.email && (
                <motion.span
                  className="absolute left-12 text-sm text-brand-secondary max-w-[220px]"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                >
                  {errors.email}
                </motion.span>
              )}
            </motion.div>
            <motion.input
              id="email"
              type="email"
              value={values.email}
              ref={fieldRef}
              onChange={handleChange}
              className={cx(
                "bg-white p-[10px] rounded-[3px] text-base focus:outline-brand-secondary border",
                {
                  "border-brand-secondary": errors.email && touched.email,
                }
              )}
              placeholder="Enter email"
            />
          </motion.div>
          <motion.div
            className="relative flex flex-col space-y-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <motion.div className="flex items-center space-x-3">
              <motion.label
                htmlFor="password"
                className={`${poppins.className} text-base font-bold`}
              >
                Password
              </motion.label>
              {errors.password && touched.password && (
                <motion.span
                  className="absolute left-20 text-sm text-brand-secondary max-w-[220px]"
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                >
                  {errors.password}
                </motion.span>
              )}
            </motion.div>
            <motion.div className="relative">
              <motion.input
                id="password"
                type={isPassVisible ? "text" : "password"}
                value={values.password}
                onChange={handleChange}
                className={cx(
                  "bg-white p-[10px] rounded-[3px] text-base focus:outline-brand-secondary border w-full pr-12",
                  {
                    "border-brand-secondary":
                      errors.password && touched.password,
                  }
                )}
                placeholder="Enter password"
              />
              <motion.span
                className="group absolute right-4 top-2.5 cursor-pointer"
                onClick={handlePasswordVisibility}
              >
                <i
                  className={cx("fas fa-eye transition duration-200", {
                    "text-brand-secondary/50 group-hover:text-brand-secondary":
                      !isPassVisible,
                    "text-brand-secondary": isPassVisible,
                  })}
                />
              </motion.span>
            </motion.div>
          </motion.div>
          <motion.button
            type="submit"
            className="relative flex justify-center items-center px-[35px] py-[13px] text-white border border-brand-secondary bg-transparent rounded-[3px] font-semibold text-base btn-animate transform duration-300 overflow-hidden z-10 hover:text-brand-secondary focus:outline-none"
            initial={{ opacity: 0, transitionDuration: "0.1s" }}
            animate={{ opacity: 1, transitionDuration: "0.1s" }}
          >
            {processLoader ? <Loader classNames="sm" /> : "Login"}
          </motion.button>
        </motion.form>
        {!!error && (
          <motion.span
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            className="absolute -bottom-10 text-brand-secondary bg-brand-secondary/10 px-4 py-1 rounded-md w-full"
          >
            <i className="fas fa-warning mr-2"></i>
            {error}
          </motion.span>
        )}
      </motion.div>
    </div>
  );
};

export default Login;
