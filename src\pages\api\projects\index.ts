import { pool } from "@utils/dbConnect";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const userId = req.query.userId;
  if (req.method !== "GET") {
    return res.status(405).json({ error: "Method is not allowed" });
  }
  try {
    const client = await pool.connect();
    const result = await pool.query(
      "SELECT * FROM user_projects WHERE user_id = $1",
      [userId]
    );
    const projects = result.rows;
    client.release();
    res.status(200).json(projects);
  } catch (error: any) {
    console.error(error.message);
    res.status(500).json({ error: error.message });
  }
}
