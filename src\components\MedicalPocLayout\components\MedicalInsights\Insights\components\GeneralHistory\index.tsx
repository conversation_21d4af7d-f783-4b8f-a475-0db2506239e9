import React from "react";
import { motion } from "framer-motion";
import { GeneralHistoryProps } from "./typings";

const GeneralHistory: React.FC<GeneralHistoryProps> = ({
  generalHistories,
  viewPDF,
}) => {
  const family_histories = generalHistories
    ?.map((data) => {
      return {
        document: data?.document_name,
        history: data?.general_history?.family_history,
      };
    })
    ?.reduce((acc: any, curr: any) => {
      if (
        curr?.history?.page_no !== null &&
        Object.keys(curr?.history?.values)?.length > 0
      ) {
        acc.push(curr);
      }
      return acc;
    }, []);

  const social_histories = generalHistories
    ?.map((data) => {
      return {
        document: data?.document_name,
        history: data?.general_history?.social_history,
      };
    })
    ?.reduce((acc: any, curr: any) => {
      if (
        curr?.history?.page_no !== null &&
        Object.keys(curr?.history?.values)?.length > 0
      ) {
        acc.push(curr);
      }
      return acc;
    }, []);

  const psychiatric_injuries = generalHistories
    ?.map((data) => {
      return {
        document: data?.document_name,
        history: data?.general_history?.psychiatric_injury,
      };
    })
    ?.reduce((acc: any, curr: any) => {
      if (
        curr?.history?.page_no !== null &&
        curr?.history?.values?.length > 0
      ) {
        acc.push(curr);
      }
      return acc;
    }, []);

  const yesOrNo = (value: string) => {
    switch (value) {
      case "Yes":
        return <i className="fas fa-circle-check check-icon"></i>;
      case "No":
        return <i className="fas fa-circle-xmark cross-icon"></i>;
      default:
        return "-";
    }
  };
  return (
    <div className="overflow-hidden rounded-b-xl">
      {generalHistories?.length === 0 ? (
        <motion.div
          className="text-center border m-4 rounded-lg text-brand-secondary font-semibold px-12 py-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          No general history available.
        </motion.div>
      ) : (
        <>
          <motion.div
            className="flex flex-col font-medium"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{
              duration: 0.3,
            }}
          >
            <div className="flex">
              <span className="w-[300px] border-y-2 border-r-2 px-12 py-3 font-semibold">
                Family History
              </span>
              <div className="w-full px-6 py-3 border-y-2 overflow-x-auto c-scroll">
                {family_histories && family_histories.length > 0 ? (
                  <div className="flex flex-col min-w-max">
                    <div className="flex bg-brand-secondary/20 py-2 px-4 gap-x-6">
                      <span className="flex-[0.5] font-semibold">Page #</span>
                      <span className="flex-[2] font-semibold">
                        Document Name
                      </span>
                      {Object.keys(family_histories?.[0]?.history?.values)?.map(
                        (dt: string, index: number) => (
                          <span className="flex-1 font-semibold" key={index}>
                            {dt?.charAt(0)?.toUpperCase() + dt?.slice(1)}
                          </span>
                        )
                      )}
                    </div>
                    {family_histories.map((data: any, index: number) => (
                      <div className="flex flex-col min-w-max" key={index}>
                        <div className="flex py-2 px-4 gap-x-6">
                          <span
                            className="flex-[0.5] text-brand-secondary font-semibold cursor-pointer underline relative left-6"
                            onClick={() =>
                              viewPDF(data?.history?.page_no, data?.document)
                            }
                          >
                            {data?.history?.page_no || "-"}
                          </span>
                          <span
                            className="flex-[2] break-all font-semibold text-brand-secondary underline cursor-pointer"
                            onClick={() =>
                              viewPDF(data?.history?.page_no, data?.document)
                            }
                          >
                            {data?.document || "-"}
                          </span>
                          {Object.values(data?.history?.values)?.map(
                            (val: any, index: number) => (
                              <span className="flex-1" key={index}>
                                {val}
                              </span>
                            )
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <span className="text-center text-brand-secondary font-semibold">
                    No family history available
                  </span>
                )}
              </div>
            </div>

            <div className="flex">
              <span className="w-[300px] border-b-2 border-r-2 px-12 py-3 font-semibold">
                Social History
              </span>
              <div className="w-full px-6 py-3 border-b-2 overflow-x-auto">
                {social_histories && social_histories.length > 0 ? (
                  <div className="flex flex-col min-w-max">
                    <div className="flex bg-brand-secondary/20 py-2 px-4 gap-x-6">
                      <span className="flex-[0.5] font-semibold">Page #</span>
                      <span className="flex-[2] font-semibold">
                        Document Name
                      </span>
                      <span className="flex-1 font-semibold">Smoking</span>
                      <span className="flex-1 font-semibold">Alcohol</span>
                      <span className="flex-1 font-semibold">Tobacco</span>
                    </div>
                    {social_histories.map((data: any, index: number) => (
                      <div className="flex flex-col min-w-max" key={index}>
                        <div className="flex py-2 px-4 gap-x-6 items-center">
                          <span
                            className="flex-[0.5] text-brand-secondary font-semibold cursor-pointer underline relative left-6"
                            onClick={() =>
                              viewPDF(data?.history?.page_no, data?.document)
                            }
                          >
                            {data?.history?.page_no || "-"}
                          </span>
                          <span
                            className="flex-[2] break-all font-semibold text-brand-secondary underline cursor-pointer"
                            onClick={() =>
                              viewPDF(data?.history?.page_no, data?.document)
                            }
                          >
                            {data?.document || "-"}
                          </span>
                          <span className="flex-1 relative left-6">
                            {yesOrNo(data?.history?.values?.smoking)}
                          </span>
                          <span className="flex-1 relative left-6">
                            {yesOrNo(data?.history?.values?.alcohol)}
                          </span>
                          <span className="flex-1 relative left-6">
                            {yesOrNo(data?.history?.values?.tobacco)}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <span className="text-center text-brand-secondary font-semibold">
                    No social history available
                  </span>
                )}
              </div>
            </div>

            <div className="flex">
              <span className="w-[300px] px-12 py-3 border-r-2 font-semibold">
                Psychiatric Injury
              </span>
              <div className="w-full px-6 py-3 overflow-x-auto">
                {psychiatric_injuries && psychiatric_injuries.length > 0 ? (
                  <div className="flex flex-col min-w-max">
                    <div className="flex bg-brand-secondary/20 py-2 px-4 gap-x-6">
                      <span className="flex-[0.4] font-semibold">Page #</span>
                      <span className="flex-1 font-semibold">
                        Document Name
                      </span>
                      <span className="flex-1 font-semibold">Injuries</span>
                    </div>
                    {psychiatric_injuries.map((data: any, index: number) => (
                      <div className="flex flex-col min-w-max" key={index}>
                        <div className="flex py-2 px-4 gap-x-6">
                          <span
                            className="flex-[0.4] text-brand-secondary font-semibold cursor-pointer underline relative left-6"
                            onClick={() =>
                              viewPDF(data?.history?.page_no, data?.document)
                            }
                          >
                            {data?.history?.page_no || "-"}
                          </span>
                          <span
                            className="flex-1 break-all font-semibold text-brand-secondary underline cursor-pointer relative left-2"
                            onClick={() =>
                              viewPDF(data?.history?.page_no, data?.document)
                            }
                          >
                            {data?.document}
                          </span>
                          <ul className="flex-1 list-disc ml-14">
                            {data?.history?.values?.length > 0 ? (
                              data?.history?.values?.map(
                                (value: any, index: number) => (
                                  <li key={index}>{value || "-"}</li>
                                )
                              )
                            ) : (
                              <span>-</span>
                            )}
                          </ul>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <span className="text-center text-brand-secondary font-semibold">
                    No psychiatric injuries available
                  </span>
                )}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </div>
  );
};

export default GeneralHistory;
