const fs = require('fs');
const path = require('path');
const { logger } = require('./logger');

/**
 * Cron Job Monitoring and Analysis Utilities
 */
class CronMonitor {
  constructor() {
    this.logsDir = path.join(process.cwd(), 'logs');
    this.metricsFile = path.join(this.logsDir, 'metrics.json');
    this.metrics = this.loadMetrics();
  }

  /**
   * Load existing metrics from file
   */
  loadMetrics() {
    try {
      if (fs.existsSync(this.metricsFile)) {
        const data = fs.readFileSync(this.metricsFile, 'utf8');
        return JSON.parse(data);
      }
    } catch (error) {
      logger.warn('Failed to load metrics file', { error: error.message });
    }
    
    return {
      totalExecutions: 0,
      totalMessages: 0,
      totalErrors: 0,
      averageExecutionTime: 0,
      lastExecution: null,
      dailyStats: {},
      errorPatterns: {}
    };
  }

  /**
   * Save metrics to file
   */
  saveMetrics() {
    try {
      fs.writeFileSync(this.metricsFile, JSON.stringify(this.metrics, null, 2));
    } catch (error) {
      logger.error('Failed to save metrics', { error: error.message });
    }
  }

  /**
   * Record a cron execution
   */
  recordExecution(executionData) {
    const today = new Date().toISOString().split('T')[0];
    
    // Update overall metrics
    this.metrics.totalExecutions++;
    this.metrics.totalMessages += executionData.totalMessagesProcessed || 0;
    this.metrics.totalErrors += executionData.totalErrors || 0;
    this.metrics.lastExecution = new Date().toISOString();
    
    // Update average execution time
    if (executionData.executionTimeMs) {
      const currentAvg = this.metrics.averageExecutionTime || 0;
      const totalExecs = this.metrics.totalExecutions;
      this.metrics.averageExecutionTime = 
        (currentAvg * (totalExecs - 1) + executionData.executionTimeMs) / totalExecs;
    }
    
    // Update daily stats
    if (!this.metrics.dailyStats[today]) {
      this.metrics.dailyStats[today] = {
        executions: 0,
        messages: 0,
        errors: 0,
        totalExecutionTime: 0
      };
    }
    
    this.metrics.dailyStats[today].executions++;
    this.metrics.dailyStats[today].messages += executionData.totalMessagesProcessed || 0;
    this.metrics.dailyStats[today].errors += executionData.totalErrors || 0;
    this.metrics.dailyStats[today].totalExecutionTime += executionData.executionTimeMs || 0;
    
    // Clean up old daily stats (keep only last 30 days)
    this.cleanupOldStats();
    
    this.saveMetrics();
  }

  /**
   * Record an error pattern
   */
  recordError(errorMessage, context = {}) {
    const errorKey = this.categorizeError(errorMessage);
    
    if (!this.metrics.errorPatterns[errorKey]) {
      this.metrics.errorPatterns[errorKey] = {
        count: 0,
        firstSeen: new Date().toISOString(),
        lastSeen: null,
        examples: []
      };
    }
    
    this.metrics.errorPatterns[errorKey].count++;
    this.metrics.errorPatterns[errorKey].lastSeen = new Date().toISOString();
    
    // Keep only last 5 examples
    if (this.metrics.errorPatterns[errorKey].examples.length >= 5) {
      this.metrics.errorPatterns[errorKey].examples.shift();
    }
    
    this.metrics.errorPatterns[errorKey].examples.push({
      timestamp: new Date().toISOString(),
      message: errorMessage,
      context
    });
    
    this.saveMetrics();
  }

  /**
   * Categorize errors for pattern analysis
   */
  categorizeError(errorMessage) {
    const message = errorMessage.toLowerCase();
    
    if (message.includes('http') && message.includes('500')) return 'HTTP_500_ERROR';
    if (message.includes('http') && message.includes('404')) return 'HTTP_404_ERROR';
    if (message.includes('http') && message.includes('timeout')) return 'HTTP_TIMEOUT';
    if (message.includes('connection') && message.includes('refused')) return 'CONNECTION_REFUSED';
    if (message.includes('sqs')) return 'SQS_ERROR';
    if (message.includes('json') && message.includes('parse')) return 'JSON_PARSE_ERROR';
    if (message.includes('invalid') && message.includes('format')) return 'INVALID_FORMAT';
    
    return 'OTHER_ERROR';
  }

  /**
   * Clean up old daily stats
   */
  cleanupOldStats() {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const cutoffDate = thirtyDaysAgo.toISOString().split('T')[0];
    
    Object.keys(this.metrics.dailyStats).forEach(date => {
      if (date < cutoffDate) {
        delete this.metrics.dailyStats[date];
      }
    });
  }

  /**
   * Get current metrics summary
   */
  getMetricsSummary() {
    const today = new Date().toISOString().split('T')[0];
    const todayStats = this.metrics.dailyStats[today] || {
      executions: 0,
      messages: 0,
      errors: 0,
      totalExecutionTime: 0
    };
    
    return {
      overall: {
        totalExecutions: this.metrics.totalExecutions,
        totalMessages: this.metrics.totalMessages,
        totalErrors: this.metrics.totalErrors,
        averageExecutionTime: Math.round(this.metrics.averageExecutionTime),
        lastExecution: this.metrics.lastExecution,
        errorRate: this.metrics.totalExecutions > 0 ? 
          (this.metrics.totalErrors / this.metrics.totalExecutions * 100).toFixed(2) + '%' : '0%'
      },
      today: {
        ...todayStats,
        averageExecutionTime: todayStats.executions > 0 ? 
          Math.round(todayStats.totalExecutionTime / todayStats.executions) : 0,
        errorRate: todayStats.executions > 0 ? 
          (todayStats.errors / todayStats.executions * 100).toFixed(2) + '%' : '0%'
      },
      topErrors: this.getTopErrors(),
      recentDays: this.getRecentDaysStats()
    };
  }

  /**
   * Get top error patterns
   */
  getTopErrors(limit = 5) {
    return Object.entries(this.metrics.errorPatterns)
      .sort(([,a], [,b]) => b.count - a.count)
      .slice(0, limit)
      .map(([type, data]) => ({
        type,
        count: data.count,
        lastSeen: data.lastSeen,
        firstSeen: data.firstSeen
      }));
  }

  /**
   * Get recent days statistics
   */
  getRecentDaysStats(days = 7) {
    const result = [];
    const today = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      const stats = this.metrics.dailyStats[dateStr] || {
        executions: 0,
        messages: 0,
        errors: 0,
        totalExecutionTime: 0
      };
      
      result.push({
        date: dateStr,
        ...stats,
        averageExecutionTime: stats.executions > 0 ? 
          Math.round(stats.totalExecutionTime / stats.executions) : 0
      });
    }
    
    return result;
  }

  /**
   * Check for health issues
   */
  checkHealth() {
    const issues = [];
    const summary = this.getMetricsSummary();
    
    // Check if cron hasn't run recently
    if (this.metrics.lastExecution) {
      const lastExec = new Date(this.metrics.lastExecution);
      const now = new Date();
      const minutesSinceLastExec = (now - lastExec) / (1000 * 60);
      
      if (minutesSinceLastExec > 10) { // More than 10 minutes
        issues.push({
          type: 'STALE_EXECUTION',
          message: `Last execution was ${Math.round(minutesSinceLastExec)} minutes ago`,
          severity: minutesSinceLastExec > 60 ? 'HIGH' : 'MEDIUM'
        });
      }
    }
    
    // Check error rate
    const errorRate = parseFloat(summary.today.errorRate);
    if (errorRate > 50) {
      issues.push({
        type: 'HIGH_ERROR_RATE',
        message: `Today's error rate is ${errorRate}%`,
        severity: 'HIGH'
      });
    } else if (errorRate > 20) {
      issues.push({
        type: 'ELEVATED_ERROR_RATE',
        message: `Today's error rate is ${errorRate}%`,
        severity: 'MEDIUM'
      });
    }
    
    // Check execution time
    if (summary.today.averageExecutionTime > 30000) { // More than 30 seconds
      issues.push({
        type: 'SLOW_EXECUTION',
        message: `Average execution time today is ${summary.today.averageExecutionTime}ms`,
        severity: 'MEDIUM'
      });
    }
    
    return {
      healthy: issues.length === 0,
      issues,
      summary
    };
  }
}

module.exports = CronMonitor;
