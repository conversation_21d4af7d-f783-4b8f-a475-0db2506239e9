import { Project } from "@typings";
import { MedicalResponse } from "../MedicalResult/typings";

export interface InsightsProps {
  isSample: boolean;
  sampleNo: number;
  project: Project;
  activeSection: string;
  handleSectionTab: (tab: string) => void;
  handleCurrentPDFPage: React.Dispatch<React.SetStateAction<number>>;
  medicalDetails: MedicalResponse[];
  handlePDFModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleCurrentPDF: React.Dispatch<
    React.SetStateAction<{ pageNo: number; documentName: string }>
  >;
  handlePDFPageData: React.Dispatch<React.SetStateAction<string | null>>;
}
