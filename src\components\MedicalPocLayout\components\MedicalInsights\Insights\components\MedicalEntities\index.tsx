import React from "react";
import { motion } from "framer-motion";
import cx from "classnames";
import { EntitiesProps } from "./typings";

const DisplayDiagnosis = (diagnosis: any): JSX.Element => {
  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex flex-col">
        <span className="font-semibold underline">Allergies</span>
        <ul className="list-disc">
          {diagnosis?.allergies?.length > 0 ? (
            diagnosis?.allergies?.map((allergy: string, index: number) => (
              <li key={index} className="ml-6">
                {allergy}
              </li>
            ))
          ) : (
            <span>-</span>
          )}
        </ul>
      </div>
      <div className="flex flex-col">
        <span className="font-semibold underline">PMH</span>
        <ul className="list-disc">
          {diagnosis?.pmh?.length > 0 ? (
            diagnosis?.pmh?.map((pmh: string, index: number) => (
              <li key={index} className="ml-6">
                {pmh}
              </li>
            ))
          ) : (
            <span>-</span>
          )}
        </ul>
      </div>
      <div className="flex flex-col">
        <span className="font-semibold underline">Others</span>
        <ul className="list-disc">
          {diagnosis?.others?.length > 0 ? (
            diagnosis?.others?.map((other: string, index: number) => (
              <li key={index} className="ml-6">
                {other}
              </li>
            ))
          ) : (
            <span>-</span>
          )}
        </ul>
      </div>
    </div>
  );
};
const DisplayTreatments = (treatments: any): JSX.Element => {
  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex flex-col">
        <span className="font-semibold underline">PMH</span>
        <ul className="list-disc">
          {treatments?.pmh?.length > 0 ? (
            treatments?.pmh?.map((pmh: string, index: number) => (
              <li key={index} className="ml-6">
                {pmh}
              </li>
            ))
          ) : (
            <span>-</span>
          )}
        </ul>
      </div>
      <div className="flex flex-col">
        <span className="font-semibold underline">Others</span>
        <ul className="list-disc">
          {treatments?.others?.length > 0 ? (
            treatments?.others?.map((other: string, index: number) => (
              <li key={index} className="ml-6">
                {other}
              </li>
            ))
          ) : (
            <span>-</span>
          )}
        </ul>
      </div>
    </div>
  );
};
const DisplayMedications = (medications: any): JSX.Element => {
  return (
    <div className="flex flex-col gap-y-4">
      <div className="flex flex-col">
        <span className="font-semibold underline">PMH</span>
        {medications?.pmh?.length > 0 ? (
          medications?.pmh?.map((pmhObj: any, index: number) => (
            <div className="flex flex-col" key={index}>
              <div className="flex gap-x-2 items-center">
                <span className="font-semibold">Name: </span>
                <span>{pmhObj?.name || "-"}</span>
              </div>
              <div className="flex gap-x-2 items-center">
                <span className="font-semibold">Dosage: </span>
                <span>{pmhObj?.dosage || "-"}</span>
              </div>
            </div>
          ))
        ) : (
          <span>-</span>
        )}
      </div>
      <div className="flex flex-col">
        <span className="font-semibold underline">Others</span>
        {medications?.others?.length > 0 ? (
          medications?.others?.map((other: any, index: number) => (
            <div className="flex flex-col" key={index}>
              <div className="flex gap-x-2 items-center">
                <span className="font-semibold">Name: </span>
                <span>{other?.name || "-"}</span>
              </div>
              <div className="flex gap-x-2 items-center">
                <span className="font-semibold">Dosage: </span>
                <span>{other?.dosage || "-"}</span>
              </div>
            </div>
          ))
        ) : (
          <span>-</span>
        )}
      </div>
    </div>
  );
};

const MedicalEntities: React.FC<EntitiesProps> = ({
  medicalEntities,
  viewPDF,
}) => {
  const modifiedEntities = medicalEntities?.flatMap((doc) =>
    doc?.entities?.map((entry) => ({
      name: doc?.name,
      page_no: entry?.page_no,
      diagnosis: entry?.diagnosis,
      treatments: entry?.treatments,
      medications: entry?.medications,
    }))
  );
  return (
    <div className="overflow-hidden rounded-b-xl">
      {modifiedEntities?.length === 0 ? (
        <motion.div
          className="text-center border m-4 rounded-lg text-brand-secondary font-semibold px-12 py-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          No medical entities available.
        </motion.div>
      ) : (
        <div className="overflow-x-auto c-scroll">
          <motion.table
            className="table-fixed w-full text-left border-collapse"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <thead className="bg-brand-secondary/30">
              <tr>
                <th className="px-6 py-3 w-48">Document Name</th>
                <th className="px-6 py-3 w-24 text-center">Page #</th>
                <th className="px-6 py-3 w-96">Diagnoses</th>
                <th className="px-6 py-3 w-96">Treatments</th>
                <th className="px-6 py-3 w-96">Medications</th>
              </tr>
            </thead>
            <tbody>
              {modifiedEntities?.map(
                (
                  { name, page_no, diagnosis, medications, treatments }: any,
                  index: number
                ) => (
                  <motion.tr
                    key={index}
                    className={cx({
                      "bg-brand-gray": index % 2 !== 0,
                      "bg-white": index % 2 === 0,
                    })}
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <td
                      className="align-top px-6 py-3 break-all text-brand-secondary font-semibold cursor-pointer underline"
                      onClick={() => viewPDF(1, name)}
                    >
                      {name || "-"}
                    </td>
                    <td
                      className="align-top px-6 py-3 text-center text-brand-secondary font-semibold cursor-pointer underline"
                      onClick={() => viewPDF(page_no, name)}
                    >
                      {page_no || "-"}
                    </td>
                    <td className="align-top px-6 py-3">
                      {DisplayDiagnosis(diagnosis) || "-"}
                    </td>
                    <td className="align-top px-6 py-3">
                      {DisplayTreatments(treatments) || "-"}
                    </td>
                    <td className="align-top px-6 py-3">
                      {DisplayMedications(medications) || "-"}
                    </td>
                  </motion.tr>
                )
              )}
            </tbody>
          </motion.table>
        </div>
      )}
    </div>
  );
};

export default MedicalEntities;
