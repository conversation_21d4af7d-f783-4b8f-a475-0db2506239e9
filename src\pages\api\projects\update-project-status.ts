import { pool } from "@utils/dbConnect";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "PATCH") {
    return res.status(405).json({ error: "Method is not allowed" });
  }

  const { projectId, status } = JSON.parse(req.body);
  try {
    const client = await pool.connect();
    await pool.query(
      "UPDATE user_projects SET status = $1, updated_at = now() WHERE project_id = $2",
      [status, projectId]
    );
    client.release();
    res.status(204).end();
  } catch (error: any) {
    console.error(error.message);
    res.status(500).json({ error: error.message });
  }
}
