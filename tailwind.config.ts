import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      screens: {
        md: "950px",
        lg: "1300px",
      },
      backgroundImage: {
        "gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
        "gradient-conic":
          "conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
      },
      fontFamily: {
        sans: ["Poppins", "sans-serif"],
      },
      colors: {
        "brand-white": "#F5F5F5",
        "brand-black": "#383838",
        "brand-primary": "#5495FF",
        "brand-gray": "#EFEFEF",
        "brand-secondary": "#F05443",
        "brand-warning": "#FBB03B",
        "gradient-black": "#101010",
      },
    },
  },
  plugins: [],
};
export default config;
