import { <PERSON>lew<PERSON> } from "next/font/google";
import React from "react";
import { motion } from "framer-motion";
import { Sample } from "@typings";
import { Download } from "@public/assets/icons";
import Image from "next/image";
import { SamplesGridProps } from "./typings";
import cx from "classnames";

const raleway = Raleway({
  display: "swap",
  weight: ["400", "500", "600"],
  subsets: ["latin"],
});

const SamplesGrid: React.FC<SamplesGridProps> = ({
  samples,
  sampleIndex,
  selectFile,
  handleDownloadSample,
}) => {
  console.log(
    "samples data inside samples grid",
    samples,
    sampleIndex,
    selectFile
  );
  return (
    <motion.div
      className={cx("col-span-2  p-6 rounded-xl shadow-md bg-white", {
        "!bg-brand-secondary/5 !shadow-brand-secondary/20": sampleIndex > -1,
      })}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div
        className={`${raleway.className} flex justify-center gap-5 leading-5 text-center font-medium flex-wrap`}
      >
        {samples?.map((sample: Sample, parentIndex: number) => (
          <motion.div
            className={cx(
              "relative flex flex-col space-y-4 items-center rounded-lg bshadow p-3 transition duration-200 max-w-[136px] cursor-default !bg-white",
              {
                "bshadow-secondary-light": sampleIndex === parentIndex,
              }
            )}
            key={sample?.fileName}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            <span className="absolute top-0 left-0 bg-brand-secondary/80 rounded-tl-lg rounded-br-lg px-2 text-[10px] before:absolute before:h-[20px] before:w-[22px] text-white before:bg-white before:blur-[6px] before:top-0 before:left-[21px] before:skew-x-[-25deg] before:opacity-0 latest-animation before:z-40 overflow-hidden">
              Sample
            </span>
            {sampleIndex === parentIndex && (
              <span className="absolute -top-5 -right-1">
                <i className="fas fa-circle-check text-brand-secondary/60"></i>
              </span>
            )}
            <span className="h-[40px] line-clamp-2" title={sample?.title}>
              {sample?.title}
            </span>
            <motion.span
              initial={{ scale: 1 }}
              transition={{ scale: { duration: 0.2 } }}
              whileTap={{ scale: 0.9 }}
              className="focus:outline-none"
            >
              <Image
                src={sample?.path!}
                className={cx(
                  "flex justify-center items-center rounded-lg w-[110px] h-[111px] cursor-pointer bg-white bshadow object-cover focus:outline-none"
                )}
                width={110}
                height={111}
                alt={sample?.title || "Sample Image"}
                priority
                onClick={() => selectFile(sample, parentIndex)}
              />
            </motion.span>
            <motion.button
              className="absolute bottom-2 right-2 z-10 cursor-pointer"
              onClick={() => {
                handleDownloadSample(sample);
              }}
              whileHover={{
                scale: 0.9,
              }}
            >
              <Download />
            </motion.button>
          </motion.div>
        ))}
      </div>
    </motion.div>
  );
};

export default SamplesGrid;
