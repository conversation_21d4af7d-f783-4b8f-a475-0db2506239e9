import React from "react";
import { motion } from "framer-motion";
import { Ralew<PERSON> } from "next/font/google";
import { PocHeaderProps } from "./typings";

const raleway = Raleway({
  display: "swap",
  weight: ["400", "500", "600"],
  subsets: ["latin"],
});

const PocHeader: React.FC<PocHeaderProps> = ({ src, title, subtitle }) => {
  return (
    <motion.div
      className="flex flex-col lg:flex-row items-center mt-5 mb-12 relative top-32"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <img src={src} alt={title} className="lg:w-1/2 w-full" />
      <div className="lg:-translate-x-32 translate-x-0 -translate-y-32 mx-8 lg:mx-0 lg:translate-y-0 bg-white p-10 flex flex-col space-y-4 justify-between rounded-lg text-brand-black transform duration-300 bshadow w-full">
        <div
          className={`bg-brand-warning px-4 rounded-sm font-semibold w-fit ${raleway.className}`}
        >
          POCs
        </div>
        <h1 className="text-5xl font-bold">{title}</h1>
        <p className={`${raleway.className} text-lg font-medium max-w-4xl`}>
          {subtitle}
        </p>
      </div>
    </motion.div>
  );
};

export default PocHeader;
