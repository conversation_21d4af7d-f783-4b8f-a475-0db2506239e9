import { PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import formidable, { File as FormidableFile } from "formidable";
import fs from "fs";
import { awsValues } from "@constants/aws";
import { NextApiRequest, NextApiResponse } from "next";
import { MAX_MEDICAL_FILE_SIZE } from "@constants";
import { buckets } from "@constants/buckets";

interface RequestProps extends NextApiRequest {
  formData: FormidableFile | any;
  userId: string | any;
  keys: string[] | any;
}

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req: RequestProps, res: NextApiResponse) {
  if (req.method !== "POST") {
    res.status(405).json({ message: "Method not allowed" });
  }
  const form = formidable({
    multiples: true,
    maxFileSize: MAX_MEDICAL_FILE_SIZE,
  });

  form.parse(req, async (err, fields, files) => {
    if (err) {
      if (err.code === "LIMIT_FILE_SIZE") {
        res.status(413).json({
          message: `File size exceeds limit of 500 MB`,
        });
      } else {
        res.status(500).json({ message: "Error parsing the form data" });
      }
      return;
    }

    const { keys } = fields;
    const userId = Array.isArray(fields.userId)
      ? fields.userId[0]
      : fields.userId;

    if (!userId || !files || !keys || Object.values(files).length === 0) {
      res.status(400).json({ message: "Missing required fields or files" });
      return;
    }
    const s3 = new S3Client({
      region: awsValues.region,
      endpoint: `https://s3.${awsValues.region}.amazonaws.com`,
    });

    const fileArray: any = [];

    Object.entries(files)?.forEach(([k, filesArr]) => {
      filesArr?.forEach((file) =>
        fileArray.push({
          originalFileName: file?.originalFilename,
          path: file?.filepath,
        })
      );
    });

    keys?.forEach((key) => {
      fileArray.map((p: any, index: number) => {
        if (
          p?.originalFileName?.toLowerCase() ===
          key?.split("request/")?.[1]?.toLowerCase()
        ) {
          fileArray[index]["key"] = key;
        }
      });
    });

    const uploadedPaths: string[] = [];

    const uploadPromises = fileArray?.map(async ({ path, key }: any) => {
      const fileStream = fs.createReadStream(path);
      const params = {
        Bucket: buckets.mi,
        Key: key,
        Body: fileStream,
      };
      const command = new PutObjectCommand(params);
      await s3.send(command);
      uploadedPaths.push(key);
    });

    try {
      await Promise.all(uploadPromises);
      res.status(200).json({
        message: "Files uploaded successfully",
        documentPaths: uploadedPaths,
      });
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: "Error uploading the file to S3" });
    }
  });
}
