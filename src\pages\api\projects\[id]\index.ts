import { pool } from "@utils/dbConnect";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "DELETE") {
    return res.status(405).json({ error: "Method is not allowed" });
  }
  const id = req.query.id;
  try {
    const client = await pool.connect();
    await pool.query("DELETE FROM user_projects WHERE project_id = $1", [id]);
    client.release();
    res.status(204).end();
  } catch (error: any) {
    console.error(error.message);
    res.status(500).json({ error: error.message });
  }
}
