import { ErrorState, Project, Sample } from "@typings";
import { MedicalResponse } from "./MedicalResult/typings";
import { FormikValues } from "formik";

export interface MedicalInsightsProps {
  handleDownloadSample: (sample: Sample) => Promise<void>;
  activeSection: string;
  handleChangeSection: React.Dispatch<React.SetStateAction<string>>;
  data: any;
  sampleIndex: number;
  medicalDetails: MedicalResponse[];
  handleSample: (path: string | any, index: number) => void;
  handleUploadLoader: React.Dispatch<React.SetStateAction<boolean>>;
  handleMultipleFiles: (
    event: React.ChangeEvent<HTMLInputElement>,
    projectId: string | any
  ) => void;
  handleMultipleDrop: (
    event: React.DragEvent<HTMLLabelElement>,
    projectId: string | any
  ) => void;
  projectStatus: string;
  handleQAModal: React.Dispatch<React.SetStateAction<boolean>>;
  handleQAError: React.Dispatch<React.SetStateAction<ErrorState>>;
  resetSample: () => void;
  projects: Project[];
  selectedProject: Project;
  handleSelectedProject: (
    project: Project,
    handleUploadLoader: React.Dispatch<React.SetStateAction<boolean>>
  ) => void;
  setTheSelectedProject: (project: Project) => void;
  handleProjectSubmission: (values: FormikValues) => any;
  projectLoading: boolean;
  sampleResult: boolean;
  handleSampleResult: React.Dispatch<React.SetStateAction<boolean>>;
  getSampleDetails: (path: string) => Promise<void>;
}
