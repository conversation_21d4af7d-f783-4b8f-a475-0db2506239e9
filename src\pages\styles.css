.text-grayish {
  @apply text-[#101010]/50;
}
.text-gradient-gray {
  @apply text-[#101010]/70;
}
.bshadow {
  box-shadow: 0px 0px 6px 0px #00000040;
}
.bshadow-sm {
  box-shadow: 0px 0px 4px 0px #00000040;
}
.hvr-rectangle-in {
  @apply bg-brand-secondary rounded-lg;
  transition-duration: 0.1s;
}
.hvr-rectangle-in:before {
  background: white;
  border-radius: 0.3rem;
  transition-duration: 0.1s;
}
.shadow-5xl:hover {
  box-shadow: 0 0 40px rgba(0, 0, 0, 0.8);
}
.bshadow-primary {
  box-shadow: 0px 0px 6px 0px #5495ff;
}
.bshadow-primary-light {
  box-shadow: 0px 0px 6px 0px #5495ff40;
}
.bshadow-warning {
  box-shadow: 0px 0px 6px 0px #febe10;
}
.bshadow-warning-light {
  box-shadow: 0px 0px 6px 0px #febe1040;
}
.bshadow-secondary {
  box-shadow: 0px 0px 4px 0px #f05443;
}
.bshadow-secondary-light {
  box-shadow: 0px 0px 10px 0px #f0544340;
}
.gradient-text {
  background: linear-gradient(
    to right,
    #febe10,
    #f47a37,
    #f05443,
    #d91a5f,
    #b41f5e
  );
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  font-size: 42px;
  line-height: 42px;
  font-weight: 700;
}
.ficon svg path {
  fill: #ffffff;
}

.btn-animate {
  min-height: 52px;
}
.btn-animate .loader-container.sm .loader {
  @apply border-t-[3px] border-t-brand-white;
}

.btn-animate:hover .loader-container.sm .loader {
  @apply border-t-[3px] border-t-brand-secondary;
}

.btn-animate::after {
  @apply flex border border-brand-secondary items-center justify-center absolute content-[''] text-white top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 bg-brand-secondary p-[450px] w-full rounded-full transform duration-300 -z-10;
}

.btn-animate:hover::after {
  @apply top-[100%] translate-y-0 content-[''];
}
