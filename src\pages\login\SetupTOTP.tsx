import React from "react";
import { motion } from "framer-motion";
import { Ralew<PERSON> } from "next/font/google";
import { useAuthState } from "@contexts/AuthContext";
import { useRouter } from "next/router";
import { slugs } from "@constants/slugs";
import { LOGIN_STAGE, VERIFY_TOTP_STAGE } from "@constants";

const raleway = Raleway({
  display: "swap",
  weight: ["500"],
  subsets: ["latin"],
});

const SetupTotp: React.FC = () => {
  const router = useRouter();
  const { qrDataUrl, handleStageChange } = useAuthState();

  return (
    <div className="flex flex-col items-start md:items-center space-y-8 justify-center bg-brand-white w-full md:w-[600px] px-16 md:px-8">
      <motion.div className="relative flex flex-col space-y-1 w-full md:w-[350px]">
        <motion.h1
          className="gradient-text"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          TOTP Setup
        </motion.h1>
        <motion.span
          className={`${raleway.className} text-sm font-medium`}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          Scan this QR code with your authenticator app:
        </motion.span>
        {qrDataUrl ? (
          <motion.img
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            src={qrDataUrl}
            className="flex justify-center items-center !my-8 border mx-auto md:mx-0 border-red-100 rounded"
            alt="QR Code"
          />
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex justify-center items-center w-[240px] h-[240px] mx-auto md:mx-0 border border-red-100 text-red-400 px-8 text-center rounded !my-8"
          >
            Failed to generate QR code
          </motion.div>
        )}
        {qrDataUrl ? (
          <motion.button
            className="relative flex justify-center items-center px-[35px] py-[13px] text-white border border-brand-secondary bg-transparent rounded-[3px] font-semibold text-base btn-animate transform duration-300 overflow-hidden z-10 hover:text-brand-secondary focus:outline-none"
            initial={{ opacity: 0, transitionDuration: "0.1s" }}
            animate={{ opacity: 1, transitionDuration: "0.1s" }}
            onClick={() => {
              handleStageChange(VERIFY_TOTP_STAGE);
            }}
          >
            Login
          </motion.button>
        ) : (
          <motion.button
            className="relative flex justify-center items-center px-[35px] py-[13px] text-white border border-brand-secondary bg-transparent rounded-[3px] font-semibold text-base btn-animate transform duration-300 overflow-hidden z-10 hover:text-brand-secondary focus:outline-none"
            initial={{ opacity: 0, transitionDuration: "0.1s" }}
            animate={{ opacity: 1, transitionDuration: "0.1s" }}
            onClick={() => {
              handleStageChange(LOGIN_STAGE);
              router.push(`/${slugs.login}`);
            }}
          >
            Login again
          </motion.button>
        )}
      </motion.div>
    </div>
  );
};

export default SetupTotp;
