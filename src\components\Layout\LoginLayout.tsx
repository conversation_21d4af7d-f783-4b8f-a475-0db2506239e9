import React from "react";
import { motion } from "framer-motion";

const LoginLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <motion.div
      className="flex h-[100dvh] overflow-hidden"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
    >
      <div
        className="relative items-center bg-white justify-center px-8 hidden md:flex overflow-hidden"
        style={{
          width: "calc(100vw - 600px)",
        }}
      >
        <motion.img
          className="absolute top-0 -left-20"
          src="/assets/images/login-image.svg"
          initial={{ y: -500, rotate: 180 }}
          animate={{ y: 0 }}
          transition={{ duration: 0.5 }}
        />
        <motion.img
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
          src="/assets/images/logo-black.svg"
          transition={{ duration: 0.3 }}
        />
        <motion.img
          className="absolute bottom-0 -right-14"
          src="/assets/images/login-image.svg"
          initial={{ y: 500 }}
          animate={{ y: 0 }}
          transition={{ duration: 0.5 }}
        />
      </div>
      {children}
    </motion.div>
  );
};

export default LoginLayout;
