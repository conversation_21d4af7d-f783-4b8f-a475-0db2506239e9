import { NextRequest, NextResponse } from "next/server";
import { v4 as uuidv4 } from "uuid";

const allowedOrigins = [
  "http://localhost:3000",
  "https://mrs-demo.marutitech.com",
  "https://api-demo.marutitech.com",
  "https://staging-mrs-demo.marutitech.com",
  "https://staging-api-demo.marutitech.com",
];

const corsOptions = {
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

const getCsrfToken = () => uuidv4();
const CSRF_TOKEN_KEY = "csrf-token";

export function middleware(req: NextRequest) {
  const res = NextResponse.next();
  const origin = req.headers.get("origin") ?? "";
  const isAllowedOrigin = allowedOrigins.includes(origin);
  const isProduction = process.env.NODE_ENV === "production";

  const isPreflight = req.method === "OPTIONS";
  const isStateChangingMethod = ["POST", "PUT", "DELETE"].includes(req.method);

  if (isPreflight) {
    const preflightHeaders = {
      ...(isAllowedOrigin && { "Access-Control-Allow-Origin": origin }),
      ...corsOptions,
    };
    return NextResponse.json({}, { headers: preflightHeaders });
  }

  if (isStateChangingMethod) {
    const csrfTokenFromRequest = req.headers.get("x-csrf-token");
    const storedCsrfToken = req.cookies.get(CSRF_TOKEN_KEY)?.value;

    if (!csrfTokenFromRequest || csrfTokenFromRequest !== storedCsrfToken) {
      return new NextResponse("Forbidden", { status: 403 });
    }
  }

  if (!req.cookies.get(CSRF_TOKEN_KEY) && req.method === "GET") {
    const csrfToken = getCsrfToken();

    res.cookies.set(CSRF_TOKEN_KEY, csrfToken, {
      httpOnly: true,
      secure: isProduction,
      sameSite: "lax",
      path: "/",
    });
  }

  if (isAllowedOrigin) {
    res.headers.set("Access-Control-Allow-Origin", origin);
  }

  Object.entries(corsOptions).forEach(([key, value]) => {
    res.headers.set(key, value);
  });

  res.headers.set(
    "Content-Security-Policy",
    "default-src 'self'; script-src 'self'; style-src 'self' 'nonce-<random-value>'; img-src 'self' data:; object-src 'none'; frame-ancestors 'none'; base-uri 'self';"
  );
  res.headers.set("X-Content-Type-Options", "nosniff");
  res.headers.set("X-Frame-Options", "DENY");
  res.headers.set(
    "Strict-Transport-Security",
    "max-age=63072000; includeSubDomains; preload"
  );
  res.headers.set("X-XSS-Protection", "1; mode=block");

  return res;
}

export const config = {
  matcher: "/api/:path*",
};
