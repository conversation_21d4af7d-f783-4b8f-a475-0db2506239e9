import React from "react";
import { motion } from "framer-motion";
import cx from "classnames";
import { SummaryProps } from "./typings";
import DOMPurify from "dompurify";

const Summary: React.FC<SummaryProps> = ({ summaries, viewPDF }) => {
  return (
    <div className="flex flex-col space-y-2 border m-4 rounded-lg">
      {summaries?.length === 0 ? (
        <motion.div
          className="text-center text-brand-secondary font-semibold px-12 py-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          No summaries available.
        </motion.div>
      ) : (
        <>
          {summaries?.map(({ name, summary }, index: number) => (
            <React.Fragment key={`summary-${index}`}>
              <span
                className={cx(
                  "bg-brand-gray/60 w-max flex gap-x-2 justify-center items-center p-4 cursor-pointer",
                  {
                    "rounded-tl-lg rounded-br-lg": index === 0,
                    "rounded-tr-lg rounded-br-lg": index !== 0,
                  }
                )}
                onClick={() => viewPDF(1, name)}
              >
                <span>
                  <i className="fas fa-file-pdf text-brand-secondary" />
                </span>
                <span
                  className="font-semibold text-brand-secondary max-w-lg truncate"
                  title={name}
                >
                  {name}
                </span>
              </span>
              <motion.div
                key={index}
                className="font-medium px-12 py-6 borderbg-white text-justify w-full"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
                dangerouslySetInnerHTML={{
                  __html: summary
                    ? DOMPurify.sanitize(summary.replaceAll(/\n/g, "<br/>"))
                    : "-",
                }}
              />
            </React.Fragment>
          ))}
        </>
      )}
    </div>
  );
};

export default Summary;
