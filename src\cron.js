require("dotenv").config();
const cron = require("node-cron");
const fetch = require("node-fetch");

const {
  SQSClient,
  ReceiveMessageCommand,
  DeleteMessageCommand,
} = require("@aws-sdk/client-sqs");

const {
  logger,
  withPerformanceLogging,
  logApiCall,
  logSqsMessage,
  createLogContext,
  logMemoryUsage,
} = require("./utils/logger");

const CronMonitor = require("./utils/cronMonitor");
const cronMonitor = new CronMonitor();

const sqsClient = new SQSClient({ region: process.env.NEXT_PUBLIC_AWS_REGION });

// Log startup information
logger.info("Cron job service starting", {
  nodeVersion: process.version,
  platform: process.platform,
  pid: process.pid,
  environment: process.env.NODE_ENV || "development",
  awsRegion: process.env.NEXT_PUBLIC_AWS_REGION,
  queueUrl: process.env.NEXT_PUBLIC_SQS_CONSUME_QUEUE_URL
    ? "configured"
    : "missing",
});

const docStatus = {
  completed: "Completed",
  failed: "Failed",
};

const checkDocumentCompleteStatus = async (projectId) => {
  const url = `${process.env.NEXT_PUBLIC_API_DOMAIN}/api/projects/documents/check-document-complete-status?projectId=${projectId}`;

  return await logApiCall(url, "GET", null, async () => {
    logger.debug("Checking document completion status", { projectId });

    const response = await fetch(url);

    if (!response.ok) {
      const errorText = await response.text();
      logger.error("Failed to check document status", {
        projectId,
        status: response.status,
        statusText: response.statusText,
        errorBody: errorText,
      });
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const finalResponse = await response.json();

    logger.info("Document status check completed", {
      projectId,
      allCompleted: finalResponse?.allCompleted,
      responseKeys: Object.keys(finalResponse || {}),
    });

    return finalResponse;
  });
};

const updateDocumentStatus = async (projectId, documentName, status) => {
  const url = `${process.env.NEXT_PUBLIC_API_DOMAIN}/api/projects/documents/update-document-status`;
  const body = { projectId, documentName, status };

  return await logApiCall(url, "PATCH", body, async () => {
    logger.debug("Updating document status", {
      projectId,
      documentName,
      status,
    });

    const response = await fetch(url, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error("Failed to update document status", {
        projectId,
        documentName,
        status,
        httpStatus: response.status,
        statusText: response.statusText,
        errorBody: errorText,
      });
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    logger.info("Document status updated successfully", {
      projectId,
      documentName,
      status,
    });

    return response;
  });
};

const updateProjectStatus = async (projectId, status) => {
  const url = `${process.env.NEXT_PUBLIC_API_DOMAIN}/api/projects/update-project-status`;
  const body = { projectId, status };

  return await logApiCall(url, "PATCH", body, async () => {
    logger.debug("Updating project status", { projectId, status });

    const response = await fetch(url, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorText = await response.text();
      logger.error("Failed to update project status", {
        projectId,
        status,
        httpStatus: response.status,
        statusText: response.statusText,
        errorBody: errorText,
      });
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    logger.info("Project status updated successfully", {
      projectId,
      status,
    });

    return response;
  });
};

const processMessagesFromQueue = async () => {
  return await withPerformanceLogging("processMessagesFromQueue", async () => {
    let allMessagesProcessed = false;
    let totalMessagesProcessed = 0;
    let totalErrors = 0;
    let batchCount = 0;

    logger.info("Starting SQS message processing", {
      queueUrl: process.env.NEXT_PUBLIC_SQS_CONSUME_QUEUE_URL,
    });

    while (!allMessagesProcessed) {
      batchCount++;
      const receiveParams = {
        QueueUrl: process.env.NEXT_PUBLIC_SQS_CONSUME_QUEUE_URL,
        MessageAttributeNames: ["All"],
        MaxNumberOfMessages: 10,
        WaitTimeSeconds: 20,
      };

      try {
        logger.debug(`Starting batch ${batchCount} - polling SQS queue`, {
          batchNumber: batchCount,
          maxMessages: receiveParams.MaxNumberOfMessages,
          waitTimeSeconds: receiveParams.WaitTimeSeconds,
        });

        const receiveCommand = new ReceiveMessageCommand(receiveParams);
        const data = await sqsClient.send(receiveCommand);

        logger.debug("SQS polling completed", {
          batchNumber: batchCount,
          messagesReceived: data.Messages ? data.Messages.length : 0,
          hasMessages: !!(data.Messages && data.Messages.length > 0),
        });

        if (data.Messages && data.Messages.length > 0) {
          logger.info(
            `Processing ${data.Messages.length} messages from batch ${batchCount}`,
            {
              batchNumber: batchCount,
              messageCount: data.Messages.length,
              totalProcessedSoFar: totalMessagesProcessed,
            }
          );

          for (const message of data.Messages) {
            const messageStartTime = Date.now();
            totalMessagesProcessed++;

            try {
              logSqsMessage(message, "processing_started", {
                batchNumber: batchCount,
                messageNumber: totalMessagesProcessed,
              });

              const body = JSON.parse(message.Body);
              logger.debug("Parsed message body", {
                messageId: message.MessageId,
                bodyContent: body,
                bodyLength: message.Body.length,
              });

              const { project_id, document_name, status } = body;

              if (!project_id || !document_name || !status) {
                throw new Error(
                  `Invalid message format: missing required fields. Got: ${JSON.stringify(
                    body
                  )}`
                );
              }

              if (!docStatus[status]) {
                throw new Error(
                  `Invalid status: ${status}. Valid statuses: ${Object.keys(
                    docStatus
                  ).join(", ")}`
                );
              }

              // Update document status
              await updateDocumentStatus(
                project_id,
                document_name,
                docStatus[status]
              );

              // Check if all documents are completed
              const { allCompleted } = await checkDocumentCompleteStatus(
                project_id
              );

              logger.info("Document completion check result", {
                projectId: project_id,
                allCompleted,
                messageId: message.MessageId,
              });

              // Update project status if all documents are completed
              if (allCompleted) {
                await updateProjectStatus(project_id, "Completed");
                logger.info("Project marked as completed", {
                  projectId: project_id,
                  messageId: message.MessageId,
                });
              }

              // Delete message from queue
              const deleteParams = {
                QueueUrl: process.env.NEXT_PUBLIC_SQS_CONSUME_QUEUE_URL,
                ReceiptHandle: message.ReceiptHandle,
              };
              const deleteCommand = new DeleteMessageCommand(deleteParams);
              await sqsClient.send(deleteCommand);

              const messageProcessingTime = Date.now() - messageStartTime;
              logSqsMessage(message, "processing_completed", {
                batchNumber: batchCount,
                messageNumber: totalMessagesProcessed,
                processingTimeMs: messageProcessingTime,
                projectId: project_id,
                documentName: document_name,
                status: docStatus[status],
                allCompleted,
              });
            } catch (error) {
              totalErrors++;
              const messageProcessingTime = Date.now() - messageStartTime;

              logger.error("Failed to process SQS message", {
                messageId: message.MessageId,
                batchNumber: batchCount,
                messageNumber: totalMessagesProcessed,
                processingTimeMs: messageProcessingTime,
                error: error.message,
                stack: error.stack,
                messageBody: message.Body,
              });

              // Record error in monitoring
              cronMonitor.recordError(error.message, {
                messageId: message.MessageId,
                batchNumber: batchCount,
                messageNumber: totalMessagesProcessed,
                processingTimeMs: messageProcessingTime,
                operation: "message-processing",
              });

              // Optionally, you might want to move failed messages to a dead letter queue
              // For now, we'll just log the error and continue
            }
          }
        } else {
          allMessagesProcessed = true;
          logger.info("No more messages in queue", {
            batchNumber: batchCount,
            totalMessagesProcessed,
            totalErrors,
          });
        }
      } catch (error) {
        totalErrors++;
        logger.error("Error during SQS batch processing", {
          batchNumber: batchCount,
          error: error.message,
          stack: error.stack,
          totalMessagesProcessed,
          totalErrors,
        });

        // Continue processing despite errors
        // You might want to implement exponential backoff here
      }
    }

    logger.info("SQS message processing completed", {
      totalBatches: batchCount,
      totalMessagesProcessed,
      totalErrors,
      successRate:
        totalMessagesProcessed > 0
          ? (
              ((totalMessagesProcessed - totalErrors) /
                totalMessagesProcessed) *
              100
            ).toFixed(2) + "%"
          : "N/A",
    });

    return {
      totalMessagesProcessed,
      totalErrors,
      totalBatches: batchCount,
    };
  });
};

// Schedule cron job to run every 5 seconds
const cronExpression = "*/5 * * * * *";
logger.info("Scheduling cron job", {
  expression: cronExpression,
  description: "Every 5 seconds",
  timezone: process.env.TZ || "system default",
});

cron.schedule(cronExpression, async () => {
  const cronStartTime = Date.now();
  const cronId = Math.random().toString(36).substring(7);

  logger.info("Cron job execution started", {
    cronId,
    scheduledTime: new Date().toISOString(),
  });

  logMemoryUsage("cron-execution-start");

  try {
    const result = await processMessagesFromQueue();
    const cronExecutionTime = Date.now() - cronStartTime;

    logger.info("Cron job execution completed successfully", {
      cronId,
      executionTimeMs: cronExecutionTime,
      executionTimeFormatted: `${cronExecutionTime}ms`,
      result,
    });

    // Record metrics
    cronMonitor.recordExecution({
      executionTimeMs: cronExecutionTime,
      totalMessagesProcessed: result.totalMessagesProcessed,
      totalErrors: result.totalErrors,
      totalBatches: result.totalBatches,
    });
  } catch (error) {
    const cronExecutionTime = Date.now() - cronStartTime;

    logger.error("Cron job execution failed", {
      cronId,
      executionTimeMs: cronExecutionTime,
      error: error.message,
      stack: error.stack,
    });

    // Record error in monitoring
    cronMonitor.recordError(error.message, {
      cronId,
      executionTimeMs: cronExecutionTime,
      operation: "cron-execution",
    });

    // Record failed execution
    cronMonitor.recordExecution({
      executionTimeMs: cronExecutionTime,
      totalMessagesProcessed: 0,
      totalErrors: 1,
      totalBatches: 0,
    });
  }

  logMemoryUsage("cron-execution-end");
});

// Log when the cron job is successfully scheduled
logger.info("Cron job scheduled successfully", {
  expression: cronExpression,
  nextExecution: "Every 5 seconds",
  status: "active",
});

// Handle graceful shutdown
process.on("SIGINT", () => {
  logger.info("Received SIGINT, shutting down gracefully");
  process.exit(0);
});

process.on("SIGTERM", () => {
  logger.info("Received SIGTERM, shutting down gracefully");
  process.exit(0);
});

process.on("uncaughtException", (error) => {
  logger.error("Uncaught exception", {
    error: error.message,
    stack: error.stack,
  });
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  logger.error("Unhandled promise rejection", {
    reason: reason instanceof Error ? reason.message : reason,
    stack: reason instanceof Error ? reason.stack : undefined,
    promise: promise.toString(),
  });
});
