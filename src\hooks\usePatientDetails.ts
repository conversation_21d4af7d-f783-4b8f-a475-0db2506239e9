import {
  calculatePatientBMI,
  calculatePatientHWByDate,
  findDate,
  mostFrequentPatientDetail,
} from "@utils";

import { PatientDemographics as PDemoGraphics } from "../components/MedicalPocLayout/components/MedicalInsights/MedicalResult/typings";

export const usePatientDetails = (patientDemographics: PDemoGraphics[]) => {
  const patientName = mostFrequentPatientDetail(
    patientDemographics?.map((patient: PDemoGraphics) => patient?.patient_name)
  );
  const patientDOB = findDate(
    patientDemographics?.map(
      (patient: PDemoGraphics) => patient?.date_of_birth
    ),
    false,
    true
  );

  const patientHeights = patientDemographics
    ?.map((patient: PDemoGraphics) => patient?.height)
    ?.reduce((acc: any, curr) => {
      if (curr.value) {
        acc.push(curr);
      }
      return acc;
    }, []);

  const patientWeights = patientDemographics
    ?.map((patient: PDemoGraphics) => patient?.weight)
    ?.reduce((acc: any, curr) => {
      if (curr.value) {
        acc.push(curr);
      }
      return acc;
    }, []);

  const patientAges = patientDemographics
    ?.map((patient: PDemoGraphics) => patient?.age)
    ?.reduce((acc: any, curr) => {
      if (!!curr && Number(curr) > 18 && Number(curr) < 100) {
        acc.push(curr);
      }
      return acc;
    }, []);

  const patientHeight = calculatePatientHWByDate(patientHeights);
  const patientWeight = calculatePatientHWByDate(patientWeights);
  const patientBMI = calculatePatientBMI(patientWeight, patientHeight);
  const patientGender = mostFrequentPatientDetail(
    patientDemographics?.map((patient: PDemoGraphics) => patient?.gender)
  );
  const patientAge = patientAges
    ?.map((age: string) => Number(age))
    ?.sort((a: number, b: number) => b - a)?.[0];

  const noDemographics =
    !patientName &&
    !patientDOB &&
    !patientAge &&
    !patientGender &&
    !patientHeight &&
    !patientWeight &&
    !patientBMI;

  return {
    patientName,
    patientDOB,
    patientAge,
    patientGender,
    patientHeight,
    patientWeight,
    patientBMI,
    noDemographics,
  };
};
