import { sectionTabs, sections } from "@constants/tabs";
import cx from "classnames";
import { motion } from "framer-motion";
import React from "react";
import {
  PatientDemographics,
  Chronologies,
  MedicalEntities,
  Summary,
} from "./components";
import { InsightsProps } from "./typings";
import Dates from "./components/GeneralHistory";
import { fetchPDF, fetchPDFForSample } from "@utils/medicalApi";
import { useRouter } from "next/router";
import { useAuthState } from "@contexts/AuthContext";
import Tests from "./components/Tests";

const selectedTab = (
  active: string,
  data: any,
  viewPDF: (pageNo: number, documentName: string) => void
) => {
  const {
    patientDemographics,
    summaries,
    medicalEntities,
    medicalTests,
    generalHistories,
    medicalChronologies,
  } = data;
  switch (active) {
    case sections.summary:
      return <Summary summaries={summaries} viewPDF={viewPDF} />;
    case sections.patientDemographics:
      return <PatientDemographics patientDemographics={patientDemographics} />;
    case sections.generalHistory:
      return <Dates generalHistories={generalHistories} viewPDF={viewPDF} />;
    case sections.medicalEntities:
      return (
        <MedicalEntities medicalEntities={medicalEntities} viewPDF={viewPDF} />
      );
    case sections.tests:
      return <Tests tests={medicalTests} viewPDF={viewPDF} />;
    case sections.medicalChronology:
      return (
        <Chronologies chronologies={medicalChronologies} viewPDF={viewPDF} />
      );
    default:
  }
};

const Insights: React.FC<InsightsProps> = ({
  isSample,
  sampleNo,
  handleSectionTab,
  activeSection,
  medicalDetails,
  project,
  handlePDFModalOpen,
  handleCurrentPDF,
  handleCurrentPDFPage,
  handlePDFPageData,
}) => {
  const router = useRouter();
  const { user } = useAuthState();
  const viewPDF = async (pageNo: number, documentName: string) => {
    handlePDFModalOpen(true);
    handleCurrentPDF({ pageNo, documentName });
    handleCurrentPDFPage(pageNo);
    if (isSample) {
      const { pdfData } = await fetchPDFForSample(sampleNo, documentName);
      handlePDFPageData(pdfData);
    } else {
      const { pdfData } = await fetchPDF(
        project?.project_id,
        user?.userId || localStorage.getItem("userId"),
        documentName
      );
      handlePDFPageData(pdfData);
    }
  };

  const patientDemographics =
    medicalDetails && medicalDetails?.map((data) => data?.patient_demographics);
  const summaries =
    medicalDetails &&
    medicalDetails?.map((data) => {
      return {
        name: data?.document_name,
        summary: data?.summary?.concat("\n\n"),
      };
    });
  const medicalEntities =
    medicalDetails &&
    medicalDetails?.map((data) => {
      return {
        name: data?.document_name,
        entities: data?.medical_entities,
      };
    });

  const medicalTests =
    medicalDetails &&
    medicalDetails?.map((data) => {
      return {
        name: data?.document_name,
        tests: data?.tests,
      };
    });

  const generalHistories =
    medicalDetails &&
    medicalDetails?.map((data) => {
      return {
        document_name: data?.document_name,
        general_history: data?.general_history,
      };
    });

  const medicalChronologies =
    medicalDetails &&
    medicalDetails?.map((data) => data.medical_chronology)?.flat();

  const tabData = {
    patientDemographics,
    summaries,
    generalHistories,
    medicalEntities,
    medicalTests,
    medicalChronologies,
  };
  return (
    <>
      <div className="flex px-6 bg-white w-full">
        <div className="flex gap-x-4 font-medium flex-wrap">
          {sectionTabs.map((tab: string, index: number) => (
            <motion.span
              className={cx(
                "px-8 py-4 flex items-center justify-center cursor-pointer hover:bg-brand-secondary/10 hover:text-brand-secondary/80 rounded-full mb-3 transition duration-200 font-semibold",
                {
                  "bg-brand-secondary/10 text-brand-secondary/80":
                    activeSection === tab,
                  "bg-transparent": activeSection !== tab,
                }
              )}
              key={index}
              onClick={() => handleSectionTab(tab)}
              initial={{ opacity: 0, scale: 1 }}
              animate={{ opacity: 1 }}
              whileTap={{ scale: 0.8 }}
              transition={{
                opacity: { delay: index / (sectionTabs.length + 6) },
              }}
            >
              {tab}
            </motion.span>
          ))}
        </div>
      </div>
      <motion.div
        className="bg-white w-full rounded-xl"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.4 }}
      >
        {selectedTab(activeSection, tabData, viewPDF)}
      </motion.div>
    </>
  );
};

export default Insights;
