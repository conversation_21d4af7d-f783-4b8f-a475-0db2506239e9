import { SendMessageCommand, SQSClient } from "@aws-sdk/client-sqs";
import { awsValues } from "@constants/aws";
import { sqsValues } from "@constants/sqs";
import type { NextApiRequest, NextApiResponse } from "next";

type Data = {
  success: boolean;
  result?: any;
  error?: string;
  message?: string;
};

const sqsClient = new SQSClient({
  region: awsValues.region,
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<Data>
) {
  if (req.method === "POST") {
    const { document_paths } = req.body;

    console.log("documents path:", document_paths);

    if (!Array.isArray(document_paths) || document_paths.length === 0) {
      return res
        .status(400)
        .json({ success: false, message: "Invalid document_paths" });
    }
    try {
      const sendDocumentPathToSQS = async (documentPath: string) => {
        const params = {
          QueueUrl: sqsValues.publishQueueUrl,
          MessageBody: JSON.stringify({ document_path: documentPath }),
        };

        console.log("params", params);
        const command = new SendMessageCommand(params);
        return sqsClient.send(command);
      };

      const messagePromises = document_paths.map((documentPath: string) =>
        sendDocumentPathToSQS(documentPath)
      );

      const results = await Promise.all(messagePromises);

      console.log("results", results);

      res.status(200).json({ success: true, result: results });
    } catch (error) {
      res.status(500).json({ success: false, error: (error as Error).message });
    }
  } else {
    res.status(405).json({ success: false, message: "Method Not Allowed" });
  }
}
