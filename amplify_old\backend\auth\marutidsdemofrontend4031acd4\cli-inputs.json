{"version": "1", "cognitoConfig": {"identityPoolName": "marutidsdemofrontend4031acd4_identitypool_4031acd4", "allowUnauthenticatedIdentities": false, "resourceNameTruncated": "maruti4031acd4", "userPoolName": "marutidsdemofrontend4031acd4_userpool_4031acd4", "autoVerifiedAttributes": ["email"], "mfaConfiguration": "OFF", "mfaTypes": ["SMS Text Message"], "smsAuthenticationMessage": "Your authentication code is {####}", "smsVerificationMessage": "Your verification code is {####}", "emailVerificationSubject": "Your verification code", "emailVerificationMessage": "Your verification code is {####}", "defaultPasswordPolicy": false, "passwordPolicyMinLength": 8, "passwordPolicyCharacters": [], "requiredAttributes": ["email"], "aliasAttributes": [], "userpoolClientGenerateSecret": false, "userpoolClientRefreshTokenValidity": 30, "userpoolClientWriteAttributes": ["email"], "userpoolClientReadAttributes": ["email"], "userpoolClientLambdaRole": "maruti4031acd4_userpoolclient_lambda_role", "userpoolClientSetAttributes": false, "sharedId": "4031acd4", "resourceName": "marutidsdemofrontend4031acd4", "authSelections": "identityPoolAndUserPool", "useDefault": "default", "usernameAttributes": ["email"], "userPoolGroupList": [], "serviceName": "Cognito", "usernameCaseSensitive": false, "useEnabledMfas": true}}