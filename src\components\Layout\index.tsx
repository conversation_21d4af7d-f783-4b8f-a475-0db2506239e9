import { Poppins } from "next/font/google";
import { motion } from "framer-motion";
import { useRouter } from "next/router";
import Head from "next/head";
import { useEffect, useRef } from "react";
import { slugs } from "@constants/slugs";
import { Loader } from "..";
import { Logo } from "@public/assets/icons";
import { useAuthState } from "@contexts/AuthContext";

const poppins = Poppins({
  display: "swap",
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const Layout = ({ children }: any) => {
  const { user, loading, logoutUser, checkUserSession } = useAuthState();
  const router = useRouter();
  const headerRef = useRef<HTMLDivElement>(null);
  const handleLogout = async () => {
    await logoutUser();
  };
  useEffect(() => {
    const currentSession = async () => {
      if (localStorage.getItem("userId")) {
        await checkUserSession();
      }
    };
    currentSession();

    let lastScrollTop = window.scrollY;
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      if (headerRef.current) {
        if (scrollPosition > lastScrollTop) {
          headerRef.current.style.top = "-250px";
          headerRef.current.style.transition = "all 0.3s ease";
        } else if (scrollPosition < lastScrollTop) {
          headerRef.current.style.top = "0";
          headerRef.current.style.transition = "all 0.3s ease";
        }
        lastScrollTop = scrollPosition <= 0 ? 0 : scrollPosition;
      }
    };
    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  if (loading) {
    return <Loader />;
  } else {
    return (
      <>
        <Head>
          <title>Medical Insights | Maruti Techlabs</title>
        </Head>
        <main className={`relative ${poppins.className}`}>
          {user && (
            <div
              className="fixed z-10 top-0 bg-white bshadow w-full"
              ref={headerRef}
            >
              <motion.header
                className="p-8 flex mx-auto justify-between items-center"
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.9 }}
              >
                <span
                  className="cursor-pointer w-[200px] h-[37px]"
                  onClick={() => router.push(slugs.home)}
                >
                  <Logo />
                </span>
                <button
                  className="flex justify-center items-center text-brand-secondary border border-brand-secondary font-medium text-sm rounded-md px-4 py-1.5 hvr-rectangle-in focus:outline-none"
                  onClick={handleLogout}
                >
                  Logout
                </button>
              </motion.header>
            </div>
          )}
          {children}
        </main>
      </>
    );
  }
};
export default Layout;
