#!/usr/bin/env node

/**
 * Cron Job Monitoring CLI Tool
 * 
 * Usage:
 *   node scripts/monitor-cron.js status    - Show current status
 *   node scripts/monitor-cron.js health    - Check health
 *   node scripts/monitor-cron.js logs      - Show recent logs
 *   node scripts/monitor-cron.js errors    - Show error patterns
 */

const fs = require('fs');
const path = require('path');
const CronMonitor = require('../src/utils/cronMonitor');

const monitor = new CronMonitor();

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatDuration(ms) {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  return `${(ms / 60000).toFixed(1)}m`;
}

function showStatus() {
  console.log('\n🔍 Cron Job Status Report');
  console.log('=' .repeat(50));
  
  const summary = monitor.getMetricsSummary();
  
  console.log('\n📊 Overall Statistics:');
  console.log(`  Total Executions: ${summary.overall.totalExecutions}`);
  console.log(`  Total Messages Processed: ${summary.overall.totalMessages}`);
  console.log(`  Total Errors: ${summary.overall.totalErrors}`);
  console.log(`  Error Rate: ${summary.overall.errorRate}`);
  console.log(`  Average Execution Time: ${formatDuration(summary.overall.averageExecutionTime)}`);
  console.log(`  Last Execution: ${summary.overall.lastExecution || 'Never'}`);
  
  console.log('\n📅 Today\'s Statistics:');
  console.log(`  Executions: ${summary.today.executions}`);
  console.log(`  Messages Processed: ${summary.today.messages}`);
  console.log(`  Errors: ${summary.today.errors}`);
  console.log(`  Error Rate: ${summary.today.errorRate}`);
  console.log(`  Average Execution Time: ${formatDuration(summary.today.averageExecutionTime)}`);
  
  console.log('\n📈 Recent 7 Days:');
  summary.recentDays.forEach(day => {
    const status = day.errors > 0 ? '❌' : day.executions > 0 ? '✅' : '⚪';
    console.log(`  ${status} ${day.date}: ${day.executions} exec, ${day.messages} msgs, ${day.errors} errors`);
  });
  
  if (summary.topErrors.length > 0) {
    console.log('\n🚨 Top Error Types:');
    summary.topErrors.forEach((error, index) => {
      console.log(`  ${index + 1}. ${error.type}: ${error.count} occurrences`);
      console.log(`     Last seen: ${error.lastSeen}`);
    });
  }
}

function showHealth() {
  console.log('\n🏥 Health Check Report');
  console.log('=' .repeat(50));
  
  const health = monitor.checkHealth();
  
  if (health.healthy) {
    console.log('\n✅ System is healthy!');
  } else {
    console.log('\n⚠️  Issues detected:');
    health.issues.forEach(issue => {
      const icon = issue.severity === 'HIGH' ? '🔴' : '🟡';
      console.log(`  ${icon} ${issue.type}: ${issue.message}`);
    });
  }
  
  console.log('\n📊 Quick Stats:');
  console.log(`  Error Rate Today: ${health.summary.today.errorRate}`);
  console.log(`  Avg Execution Time: ${formatDuration(health.summary.today.averageExecutionTime)}`);
  console.log(`  Last Execution: ${health.summary.overall.lastExecution || 'Never'}`);
}

function showLogs(lines = 50) {
  console.log('\n📋 Recent Log Entries');
  console.log('=' .repeat(50));
  
  const logsDir = path.join(process.cwd(), 'logs');
  const today = new Date().toISOString().split('T')[0];
  const logFile = path.join(logsDir, `cron-${today}.log`);
  
  if (!fs.existsSync(logFile)) {
    console.log('\n⚠️  No log file found for today');
    return;
  }
  
  try {
    const logContent = fs.readFileSync(logFile, 'utf8');
    const logLines = logContent.split('\n').filter(line => line.trim());
    const recentLines = logLines.slice(-lines);
    
    console.log(`\n📄 Last ${recentLines.length} log entries:\n`);
    recentLines.forEach(line => {
      // Color code log levels
      if (line.includes('[ERROR]')) {
        console.log(`🔴 ${line}`);
      } else if (line.includes('[WARN]')) {
        console.log(`🟡 ${line}`);
      } else if (line.includes('[INFO]')) {
        console.log(`🔵 ${line}`);
      } else {
        console.log(`⚪ ${line}`);
      }
    });
  } catch (error) {
    console.log(`\n❌ Error reading log file: ${error.message}`);
  }
}

function showErrors() {
  console.log('\n🚨 Error Analysis');
  console.log('=' .repeat(50));
  
  const summary = monitor.getMetricsSummary();
  
  if (summary.topErrors.length === 0) {
    console.log('\n✅ No errors recorded!');
    return;
  }
  
  console.log('\n📊 Error Patterns:');
  summary.topErrors.forEach((error, index) => {
    console.log(`\n${index + 1}. ${error.type}`);
    console.log(`   Count: ${error.count}`);
    console.log(`   First seen: ${error.firstSeen}`);
    console.log(`   Last seen: ${error.lastSeen}`);
  });
  
  // Show error log if available
  const logsDir = path.join(process.cwd(), 'logs');
  const today = new Date().toISOString().split('T')[0];
  const errorLogFile = path.join(logsDir, `cron-error-${today}.log`);
  
  if (fs.existsSync(errorLogFile)) {
    console.log('\n📄 Recent Error Log Entries:');
    try {
      const errorContent = fs.readFileSync(errorLogFile, 'utf8');
      const errorLines = errorContent.split('\n').filter(line => line.trim()).slice(-10);
      
      errorLines.forEach(line => {
        console.log(`🔴 ${line}`);
      });
    } catch (error) {
      console.log(`❌ Error reading error log: ${error.message}`);
    }
  }
}

function showLogFiles() {
  console.log('\n📁 Available Log Files');
  console.log('=' .repeat(50));
  
  const logsDir = path.join(process.cwd(), 'logs');
  
  if (!fs.existsSync(logsDir)) {
    console.log('\n⚠️  Logs directory not found');
    return;
  }
  
  try {
    const files = fs.readdirSync(logsDir);
    const logFiles = files.filter(file => file.endsWith('.log'));
    
    if (logFiles.length === 0) {
      console.log('\n⚠️  No log files found');
      return;
    }
    
    console.log('\n📄 Log Files:');
    logFiles.forEach(file => {
      const filePath = path.join(logsDir, file);
      const stats = fs.statSync(filePath);
      const size = formatBytes(stats.size);
      const modified = stats.mtime.toISOString().split('T')[0];
      
      console.log(`  📄 ${file} (${size}, modified: ${modified})`);
    });
  } catch (error) {
    console.log(`❌ Error reading logs directory: ${error.message}`);
  }
}

// Main CLI logic
const command = process.argv[2];

switch (command) {
  case 'status':
    showStatus();
    break;
  case 'health':
    showHealth();
    break;
  case 'logs':
    const lines = parseInt(process.argv[3]) || 50;
    showLogs(lines);
    break;
  case 'errors':
    showErrors();
    break;
  case 'files':
    showLogFiles();
    break;
  default:
    console.log('\n🔧 Cron Job Monitor');
    console.log('=' .repeat(30));
    console.log('\nUsage:');
    console.log('  node scripts/monitor-cron.js status    - Show current status');
    console.log('  node scripts/monitor-cron.js health    - Check health');
    console.log('  node scripts/monitor-cron.js logs [n]  - Show recent logs (default: 50 lines)');
    console.log('  node scripts/monitor-cron.js errors    - Show error patterns');
    console.log('  node scripts/monitor-cron.js files     - List available log files');
    console.log('\nExamples:');
    console.log('  node scripts/monitor-cron.js logs 100  - Show last 100 log lines');
    console.log('  node scripts/monitor-cron.js status    - Show full status report');
    break;
}
