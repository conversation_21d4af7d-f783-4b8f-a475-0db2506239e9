//Envs
export const API_DOMAIN = process.env.NEXT_PUBLIC_API_DOMAIN || "";
export const QNA_API_DOMAIN = process.env.NEXT_PUBLIC_QNA_API_DOMAIN || "";
export const MAX_MEDICAL_FILE_SIZE =
  parseInt(process.env.NEXT_PUBLIC_MAX_MEDICAL_FILE_SIZE!, 10) || 0;
export const MAX_PAGES_LIMIT =
  parseInt(process.env.NEXT_PUBLIC_MAX_PAGES_LIMIT!, 10) || 0;
export const MAX_PROJECTS =
  parseInt(process.env.NEXT_PUBLIC_MAX_PROJECTS!, 10) || 0;
export const MAX_TOTP_LENGTH = 6;

//Logo
export const LOGO_ALT = "Maruti Techlabs";

//Regex
export const EMAIL_REGEX =
  /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

//Errors
export const EMPTY_EMAIL = "Please enter an email";
export const INVALID_EMAIL = "Please enter a valid email";
export const EMPTY_PASSWORD = "Please enter a password";
export const INVALID_PASSWORD =
  "Password length must be atleast minimum 8 characters";
export const EMPTY_PROJECT_NAME = "Please provide a project name";
export const UNIQUE_PROJECT_NAME = "Project already exists!";
export const SOMETHING_WENT_WRONG = "Something went wrong!";
export const UPLOAD_ERROR = "An error occurred while uploading the file.";
export const INVALID_SESSION =
  "Invalid session for the user, session is expired.";

//AWS Sign In Steps
export const NEW_PASSWORD_STEP = "CONFIRM_SIGN_IN_WITH_NEW_PASSWORD_REQUIRED";
export const TOTP_SETUP = "CONTINUE_SIGN_IN_WITH_TOTP_SETUP";
export const VERIFY_TOTP = "CONFIRM_SIGN_IN_WITH_TOTP_CODE";

//AWS Sign In Stages
export const LOGIN_STAGE = "LOGIN_STAGE";
export const SETUP_TOTP_STAGE = "SETUP_TOTP_STAGE";
export const VERIFY_TOTP_STAGE = "VERIFY_TOTP_STAGE";

//QR App Name
export const APP_NAME = "MRSApp";
