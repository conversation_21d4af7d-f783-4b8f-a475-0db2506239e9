apiVersion: apps/v1
kind: Deployment
metadata:
  name: mrs-frontend
  namespace: production
  labels:
    app: mrs-frontend

spec:
  replicas: 1
  selector:
    matchLabels:
      app: mrs-frontend

  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%

  template:
    metadata:
      name: mrs-frontend
      labels:
        app: mrs-frontend

    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: production
                operator: In
                values:
                - "true"
      containers:
      - name: mrs-frontend
        image: DOCKER_IMAGE
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: mrs-frontend
        resources:
          requests:
            memory: "300Mi"
            cpu: "256m"
          limits:
            memory: "600Mi"
            cpu: "300m"
        env:
        - name: NEXT_PUBLIC_MAX_PROJECTS
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_MAX_PROJECTS
        - name: NEXT_PUBLIC_API_DOMAIN
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_API_DOMAIN
        - name: NEXT_PUBLIC_QNA_API_DOMAIN
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_QNA_API_DOMAIN
        - name: NEXT_PUBLIC_MAX_MEDICAL_FILE_SIZE
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_MAX_MEDICAL_FILE_SIZE
        - name: NEXT_PUBLIC_MAX_PAGES_LIMIT
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_MAX_PAGES_LIMIT
        - name: NEXT_PUBLIC_DB_USER
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_DB_USER
        - name: NEXT_PUBLIC_DB_HOST
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_DB_HOST
        - name: NEXT_PUBLIC_DB_PORT
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_DB_PORT
        - name: NEXT_PUBLIC_DB_NAME
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_DB_NAME
        - name: NEXT_PUBLIC_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_DB_PASSWORD
        - name: NEXT_PUBLIC_AWS_REGION
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_AWS_REGION
        - name: NEXT_PUBLIC_AWS_PROJECT_REGION
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_AWS_PROJECT_REGION
        - name: NEXT_PUBLIC_AWS_COGNITO_REGION
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_AWS_COGNITO_REGION
        - name: NEXT_PUBLIC_AWS_COGNITO_IDENTITY_POOL_ID
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_AWS_COGNITO_IDENTITY_POOL_ID
        - name: NEXT_PUBLIC_AWS_USER_POOLS_ID
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_AWS_USER_POOLS_ID
        - name: NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID
        - name: NEXT_PUBLIC_SQS_PUBLISH_QUEUE_URL
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_SQS_PUBLISH_QUEUE_URL
        - name: NEXT_PUBLIC_SQS_CONSUME_QUEUE_URL
          valueFrom:
            secretKeyRef:
              name: ds-frontend-secrets
              key: NEXT_PUBLIC_SQS_CONSUME_QUEUE_URL
