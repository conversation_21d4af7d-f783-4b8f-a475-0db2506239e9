import { ProjectDocument } from "@typings";

export const createDocuments = async (
  documents: ProjectDocument[],
  csrfToken: string
) => {
  return await fetch("/api/projects/documents/create-documents", {
    method: "POST",
    headers: {
      "X-CSRF-Token": csrfToken,
    },
    body: JSON.stringify({ documents }),
  });
};

export const updateDocumentStatus = async (
  projectId: string | any,
  documentName: string | any,
  status: string,
  csrfToken: string
) => {
  return await fetch("/api/projects/documents/update-document-status", {
    method: "PATCH",
    headers: {
      "X-CSRF-Token": csrfToken,
    },
    body: JSON.stringify({ projectId, documentName, status }),
  });
};

export const checkDocumentCompleteStatus = async (projectId: string | any) => {
  return await fetch(
    `/api/projects/documents/check-document-complete-status?projectId=${projectId}`
  );
};

export const updateProjectPages = async (
  projectId: string,
  totalPages: number,
  csrfToken: string
) => {
  return await fetch("/api/projects/update-project-pages", {
    method: "PATCH",
    headers: {
      "X-CSRF-Token": csrfToken,
    },
    body: JSON.stringify({ projectId, totalPages }),
  });
};

export const updateProjectStatus = async (
  projectId: string | any,
  status: string,
  csrfToken: string
) => {
  return await fetch("/api/projects/update-project-status", {
    method: "PATCH",
    headers: {
      "X-CSRF-Token": csrfToken,
    },
    body: JSON.stringify({ projectId, status }),
  });
};

export const retrieveProject = async (projectId: string | any) => {
  return await fetch(`/api/projects/retrieve-project?projectId=${projectId}`);
};
