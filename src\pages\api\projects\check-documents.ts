import { NextApiRequest, NextApiResponse } from "next";
import { pool } from "@utils/dbConnect";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { project_id } = JSON.parse(req.body);
  try {
    const client = await pool.connect();
    const documents = await pool.query(
      "SELECT * FROM documents WHERE project_id = $1",
      [project_id]
    );
    const hasDocuments = documents?.rows?.length > 0;
    client.release();
    res.status(200).json({ exists: hasDocuments });
  } catch (error) {
    res.status(404).json({ exists: false });
  }
}
