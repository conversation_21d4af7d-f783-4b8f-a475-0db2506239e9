import type { NextApiRequest, NextApiResponse } from "next";
import {
  S3Client,
  GetObjectCommand,
  ListObjectsV2Command,
} from "@aws-sdk/client-s3";
import { Readable } from "stream";
import { awsValues } from "@constants/aws";

const s3Client = new S3Client({ region: awsValues.region });

const getJsonFromS3 = async (bucket: string, key: string) => {
  const command = new GetObjectCommand({
    Bucket: bucket,
    Key: key,
  });
  const data = await s3Client.send(command);
  const stream = data.Body as Readable;
  const chunks: Buffer[] = [];

  for await (const chunk of stream) {
    chunks.push(chunk);
  }
  return JSON.parse(Buffer.concat(chunks as any).toString("utf-8"));
};

const listS3Objects = async (bucket: string, prefix: string) => {
  const command = new ListObjectsV2Command({
    Bucket: bucket,
    Prefix: prefix,
  });

  const data = await s3Client.send(command);
  return data.Contents || [];
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { bucket, userId, projectId } = req.query;

  if (!bucket || !userId || !projectId) {
    return res.status(400).json({
      success: false,
      message: "Bucket, userId, and projectId are required",
    });
  }

  const prefix = `user-data/${userId}/${projectId}/response`;

  try {
    const s3Objects = await listS3Objects(bucket as string, prefix);

    const mergedDataArray = await Promise.all(
      s3Objects.map(async (obj) => {
        if (obj.Key) {
          const jsonData = await getJsonFromS3(bucket as string, obj.Key);
          return jsonData;
        }
        return null;
      })
    );

    const combinedData = mergedDataArray.filter(Boolean).flat();

    return res.status(200).json({ success: true, data: combinedData });
  } catch (error) {
    console.error("Error fetching from S3:", error);
    return res
      .status(500)
      .json({ success: false, error: (error as Error).message });
  }
}
