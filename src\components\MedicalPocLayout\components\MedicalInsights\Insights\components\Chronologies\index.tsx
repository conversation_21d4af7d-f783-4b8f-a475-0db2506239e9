import React from "react";
import { motion } from "framer-motion";
import cx from "classnames";
import { ChronologiesProps } from "./typings";
import { Chronology } from "../../../MedicalResult/typings";
import DOMPurify from "dompurify";

const Chronologies: React.FC<ChronologiesProps> = ({
  chronologies,
  viewPDF,
}) => {
  const mergedChronologies =
    chronologies &&
    chronologies
      ?.map((item: Chronology, index: number) => {
        return {
          id: index + 1,
          date: item?.date,
          event: item?.event,
          doctor_name: item?.doctor_name,
          hospital_name: item?.hospital_name,
          document_name: item?.document_name,
          page_no: item?.page_no,
        };
      })
      ?.sort((a, b) => (new Date(b?.date) as any) - (new Date(a?.date) as any));

  console.log("chronologies", chronologies);
  console.log("mergedChronologies", mergedChronologies);

  return (
    <div className="overflow-hidden rounded-b-xl">
      {mergedChronologies?.length === 0 ? (
        <motion.div
          className="text-center border m-4 rounded-lg text-brand-secondary font-semibold px-12 py-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          No medical chronologies available.
        </motion.div>
      ) : (
        <div className="overflow-x-auto c-scroll">
          <motion.table
            className="table-fixed w-full text-left border-collapse"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <thead className="bg-brand-secondary/30">
              <tr>
                <th className="px-6 py-3 w-48">Document Name</th>
                <th className="px-6 py-3 w-24 text-center">Page #</th>
                <th className="px-6 py-3 w-40">Date</th>
                <th className="px-6 py-3 w-48">Hospital Name</th>
                <th className="px-6 py-3 w-48">Doctor Name</th>
                <th className="px-6 py-3 w-96">Event</th>
              </tr>
            </thead>
            <tbody>
              {mergedChronologies?.map(
                (
                  {
                    id,
                    date,
                    event,
                    document_name,
                    doctor_name,
                    hospital_name,
                    page_no,
                  },
                  index
                ) => (
                  <motion.tr
                    key={id}
                    className={cx({
                      "bg-brand-gray": index % 2 !== 0,
                      "bg-white": index % 2 === 0,
                    })}
                    initial={{ opacity: 0 }}
                    whileInView={{ opacity: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <td
                      className="px-6 py-3 break-all text-brand-secondary font-semibold cursor-pointer underline"
                      onClick={() => viewPDF(1, document_name)}
                    >
                      {document_name || "-"}
                    </td>
                    <td
                      className="px-6 py-3 text-center text-brand-secondary font-semibold cursor-pointer underline"
                      onClick={() => viewPDF(page_no, document_name)}
                    >
                      {page_no || "-"}
                    </td>
                    <td className="px-6 py-3">{date || "-"}</td>
                    <td className="px-6 py-3 break-all font-semibold">
                      {hospital_name || "-"}
                    </td>
                    <td className="px-6 py-3 break-all font-semibold">
                      {doctor_name || "-"}
                    </td>
                    <td
                      className="px-6 py-3"
                      dangerouslySetInnerHTML={{
                        __html: event
                          ? DOMPurify.sanitize(event.replaceAll(/\n/g, "<br/>"))
                          : "-",
                      }}
                    />
                  </motion.tr>
                )
              )}
            </tbody>
          </motion.table>
        </div>
      )}
    </div>
  );
};

export default Chronologies;
