import { pool } from "@utils/dbConnect";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "PATCH") {
    return res.status(405).json({ error: "Method is not allowed" });
  }

  const { projectId, totalPages } = JSON.parse(req.body);
  try {
    const client = await pool.connect();
    await pool.query(
      "UPDATE user_projects SET total_pages = $1, updated_at = now() WHERE project_id = $2",
      [totalPages, projectId]
    );
    client.release();
    res.status(204).end();
  } catch (error: any) {
    console.error(error.message);
    res.status(500).json({ error: error.message });
  }
}
