import useOutsideClick from "@hooks/useOutsideClick";
import { useEffect, useRef } from "react";
import cx from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import { ModalProps } from "./typings";

const Modal: React.FC<ModalProps> = ({
  open,
  setOpen,
  styles,
  isPDF,
  children,
  pdfData,
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const onClose = () => {
    setOpen(false);
  };
  useOutsideClick(modalRef, onClose);
  useEffect(() => {
    if (open) {
      document.body.style.overflowY = "clip";
    } else {
      document.body.style.overflowY = "auto";
    }
  }, [open]);
  const currentPDFPage = pdfData?.[0];
  const setCurrentPDFPage = pdfData?.[1];
  const currentPDFTotalPages = pdfData?.[2];
  return (
    <AnimatePresence>
      {open && (
        <motion.div
          className="fixed top-0 left-0 z-[9999] flex justify-center items-center w-full h-screen bg-[rgba(0,0,0,0.7)]"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          {isPDF ? (
            <div className="flex flex-col items-center gap-y-8">
              <div className="flex gap-x-2">
                <button
                  className={cx("text-3xl md:text-4xl h-fit self-center", {
                    "cursor-default": currentPDFPage === 1,
                    "cursor-pointer": currentPDFPage !== 1,
                  })}
                  onClick={() => {
                    setCurrentPDFPage((prevPage: number) =>
                      prevPage !== 1 ? prevPage - 1 : prevPage
                    );
                  }}
                  disabled={currentPDFPage === 1}
                >
                  <motion.i
                    whileTap={currentPDFPage === 1 ? {} : { scale: 0.9 }}
                    className={cx("fa fa-circle-arrow-left", {
                      "text-gray-400": currentPDFPage === 1,
                      "text-white": currentPDFPage !== 1,
                    })}
                  ></motion.i>
                </button>
                <motion.div
                  className={cx(
                    "flex relative w-[600px] h-[300px] bg-white pt-[30px] rounded-md shadow shadow-[rgba(0,0,0,0.3)] mx-[20px]",
                    {
                      [styles]: !!styles,
                    }
                  )}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0 }}
                  transition={{ type: "spring", bounce: 0.5, duration: 0.6 }}
                >
                  <motion.div
                    className="fixed top-0 right-[6px] m-4 px-3 py-1 text-2xl border-2 text-white border-white md:px-3 md:py-1 rounded-full md:text-3xl z-[9999] cursor-pointer bg-transparent hover:text-brand-secondary hover:bg-white transition duration-200"
                    onClick={onClose}
                    whileTap={{ scale: 0.9 }}
                  >
                    &times;
                  </motion.div>
                  {children}
                </motion.div>
                <button
                  className={cx("text-3xl md:text-4xl h-fit self-center", {
                    "cursor-default": currentPDFPage === currentPDFTotalPages,
                    "cursor-pointer": currentPDFPage !== currentPDFTotalPages,
                  })}
                  onClick={() => {
                    setCurrentPDFPage((prevPage: number) =>
                      prevPage < currentPDFTotalPages ? prevPage + 1 : prevPage
                    );
                  }}
                  disabled={currentPDFPage === currentPDFTotalPages}
                >
                  <motion.i
                    whileTap={
                      currentPDFPage === currentPDFTotalPages
                        ? {}
                        : { scale: 0.9 }
                    }
                    className={cx("fa fa-circle-arrow-right", {
                      "text-gray-400": currentPDFPage === currentPDFTotalPages,
                      "text-white": currentPDFPage !== currentPDFTotalPages,
                    })}
                  ></motion.i>
                </button>
              </div>
              <span className="flex items-center justify-center font-semibold text-2xl md:text-3xl text-brand-secondary px-3 py-1 md:px-4 md:py-2 bg-white rounded-lg">
                {currentPDFPage}
              </span>
            </div>
          ) : (
            <motion.div
              ref={modalRef}
              className={cx(
                "flex relative w-[600px] h-[300px] bg-white pt-[30px] rounded-md shadow shadow-[rgba(0,0,0,0.3)] mx-[20px]",
                {
                  [styles]: !!styles,
                }
              )}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
              transition={{ type: "spring", bounce: 0.5, duration: 0.6 }}
            >
              <div
                className="absolute top-0 right-[6px] p-[10px] text-2xl z-[9999] cursor-pointer"
                onClick={onClose}
              >
                &times;
              </div>
              {children}
            </motion.div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Modal;
