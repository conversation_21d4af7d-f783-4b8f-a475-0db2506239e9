import {
  S3Client,
  GetObjectCommand,
  GetObjectOutput,
} from "@aws-sdk/client-s3";
import { awsValues } from "@constants/aws";
import { NextApiRequest, NextApiResponse } from "next";
import { Readable } from "stream";

const s3 = new S3Client({
  region: awsValues.region,
});

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { fileName } = req.query;
  const bucketName = String(req.query.bucketName);
  const bucketKey = String(req.query.bucketKey);

  try {
    const params = {
      Bucket: bucketName,
      Key: bucketKey,
    };
    const response: GetObjectOutput = await s3.send(
      new GetObjectCommand(params)
    );

    if (response.Body) {
      const bodyStream = response.Body as Readable;
      const contentType = getFileContentType(fileName);
      res.setHeader("Content-Type", contentType);
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${fileName}"`
      );
      bodyStream.on("error", (err) => {
        console.error("Stream error:", err);
        res.status(500).json({ error: "Error streaming file from S3" });
      });
      bodyStream.pipe(res);
    } else {
      res.status(404).send("File not found");
    }
  } catch (error) {
    console.error("Error fetching file from S3:", error);
    res.status(500).json({ error: "Error fetching file from S3" });
  }
}

function getFileContentType(fileName: string | any): string {
  const fileExtension = fileName?.toLowerCase()?.split(".")?.pop();
  switch (fileExtension) {
    case "pdf":
      return "application/pdf";
    case "jpg":
    case "jpeg":
      return "image/jpeg";
    case "png":
      return "image/png";
    default:
      return "application/octet-stream";
  }
}
