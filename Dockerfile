FROM node:21-slim

WORKDIR /app

# ARG NEXT_PUBLIC_AWS_REGION
# ARG NEXT_PUBLIC_AWS_PROJECT_REGION
# ARG NEXT_PUBLIC_AWS_COGNITO_REGION
# ARG NEXT_PUBLIC_AWS_COGNITO_IDENTITY_POOL_ID
# ARG NEXT_PUBLIC_AWS_USER_POOLS_ID
# ARG NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID

# ENV NEXT_PUBLIC_AWS_REGION ${NEXT_PUBLIC_AWS_REGION}  
# ENV NEXT_PUBLIC_AWS_PROJECT_REGION ${NEXT_PUBLIC_AWS_PROJECT_REGION}
# ENV NEXT_PUBLIC_AWS_COGNITO_REGION ${NEXT_PUBLIC_AWS_COGNITO_REGION}
# ENV NEXT_PUBLIC_AWS_COGNITO_IDENTITY_POOL_ID ${NEXT_PUBLIC_AWS_COGNITO_IDENTITY_POOL_ID}
# ENV NEXT_PUBLIC_AWS_USER_POOLS_ID ${NEXT_PUBLIC_AWS_USER_POOLS_ID}
# ENV NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID ${NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID}

RUN apt-get update && apt-get install -y yarn && apt-get clean && rm -rf /var/lib/apt/lists/*

# COPY .env ./
# RUN cat .env

COPY package*.json ./
COPY yarn.lock    ./

RUN yarn install \
  --no-progress \
  --non-interactive \
  --frozen-lockfile

COPY . ./

CMD ["source", ".env"]

RUN cat .env

RUN yarn build

RUN yarn global add pm2

COPY ecosystem.config.js /app/ecosystem.config.js

EXPOSE 3000

CMD ["pm2-runtime", "ecosystem.config.js"]