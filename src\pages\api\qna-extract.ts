import { QNA_API_DOMAIN, SOMETHING_WENT_WRONG } from "@constants";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: "Method is not allowed" });
  }

  const { project_path, input_query } = JSON.parse(req.body);
  try {
    const qnaUrl = `${QNA_API_DOMAIN}/medical-insights/qna-extract`;
    const result = await fetch(qnaUrl, {
      method: "POST",
      body: JSON.stringify({
        project_path,
        input_query,
      }),
    });
    if (result.ok) {
      const qnaResult = await result.json();
      res.status(200).json({ ...qnaResult });
    } else {
      res.status(500).json({ error: SOMETHING_WENT_WRONG });
    }
  } catch (error: any) {
    console.error(error.message);
    res.status(500).json({ error: error.message });
  }
}
