export interface Sample {
  sampleInd?: number;
  fileName?: string | any;
  title?: string;
  path?: string;
  jsonPath?: string;
}
export interface SampleState extends Sample {
  index?: number;
}
export interface ErrorState {
  error: boolean;
  message: string;
}
export interface Project {
  project_id: number | string;
  project_name: string;
}
export interface ProjectDocument {
  document_id: string;
  document_name: string;
  pages: number;
  project_id: string;
}
