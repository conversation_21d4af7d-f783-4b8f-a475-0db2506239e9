import { ProjectDocument } from "@typings";
import { pool } from "@utils/dbConnect";
import { NextApiRequest, NextApiResponse } from "next";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method === "POST") {
    const { documents }: { documents: ProjectDocument[] } = JSON.parse(
      req.body
    );
    const client = await pool.connect();
    try {
      await client.query("BEGIN");
      const insertQuery =
        "INSERT INTO documents (document_id, pages, project_id, status, document_name) VALUES ($1, $2, $3, $4, $5)";

      for (const document of documents) {
        const { document_id, document_name, pages, project_id } = document;
        await client.query(insertQuery, [
          document_id,
          pages,
          project_id,
          "Created",
          document_name,
        ]);
      }
      await client.query("COMMIT");

      res.status(201).json({
        ids: documents?.map(
          (document: ProjectDocument) => document?.document_id
        ),
      });
    } catch (error) {
      await client.query("ROLLBACK");
      console.error("Error during bulk insert:", error);
      res.status(500).json({ error: "An error occurred during bulk insert" });
    } finally {
      client.release();
    }
  } else {
    res.status(405).json({ error: "Method Not Allowed" });
  }
}

export function cleanup() {
  pool.end();
}
