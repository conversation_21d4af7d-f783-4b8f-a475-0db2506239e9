# Cron Job Monitoring and Debugging Guide

This document provides comprehensive information about the enhanced logging and monitoring system for the SQS message processing cron job.

## Overview

The cron job has been enhanced with:

- **Structured logging** using Winston
- **Performance monitoring** with execution time tracking
- **Error tracking** and pattern analysis
- **Health monitoring** with automated checks
- **CLI monitoring tools** for real-time insights

## Log Files

All logs are stored in the `logs/` directory with automatic rotation:

### Log Types

- **`cron-YYYY-MM-DD.log`** - General application logs (INFO level and above)
- **`cron-error-YYYY-MM-DD.log`** - Error logs only
- **`cron-debug-YYYY-MM-DD.log`** - Detailed debug logs
- **`exceptions.log`** - Uncaught exceptions
- **`rejections.log`** - Unhandled promise rejections
- **`metrics.json`** - Performance metrics and statistics

### Log Rotation

- Files rotate daily
- Compressed after rotation (`.gz`)
- Retention: 14 days for general logs, 30 days for errors, 7 days for debug
- Maximum file size: 20MB (general), 50MB (debug)

## Monitoring Commands

Use these npm scripts to monitor your cron job:

```bash
# Show comprehensive status report
npm run monitor

# Check system health
npm run monitor:health

# View recent log entries (last 50 lines)
npm run monitor:logs

# Analyze error patterns
npm run monitor:errors

# List available log files
npm run monitor:files
```

### Advanced Monitoring

```bash
# Show last 100 log lines
node scripts/monitor-cron.js logs 100

# Get detailed status
node scripts/monitor-cron.js status

# Check for health issues
node scripts/monitor-cron.js health
```

## Log Levels

The system uses standard log levels:

- **ERROR** - Critical errors that need immediate attention
- **WARN** - Warning conditions that should be monitored
- **INFO** - General information about operations
- **DEBUG** - Detailed debugging information

Set the log level using the `LOG_LEVEL` environment variable:

```bash
LOG_LEVEL=debug npm run start:cron
```

## What Gets Logged

### Cron Execution

- Start/end timestamps
- Execution duration
- Memory usage before/after
- Total messages processed
- Error counts and success rates

### SQS Operations

- Queue polling attempts
- Message counts per batch
- Individual message processing
- Message deletion confirmations

### API Calls

- Request details (URL, method, body)
- Response status and timing
- Error responses with full details

### Performance Metrics

- Processing time per message
- Batch processing statistics
- Memory usage tracking
- Success/failure rates

### Error Tracking

- Detailed error messages with stack traces
- Error categorization and patterns
- Context information (message ID, project ID, etc.)
- Retry attempts and outcomes

## Health Monitoring

The system automatically monitors:

### Execution Health

- **Stale executions** - Alerts if cron hasn't run recently
- **High error rates** - Warns when error rate exceeds thresholds
- **Slow performance** - Detects unusually long execution times

### Thresholds

- **Stale execution**: > 10 minutes since last run
- **High error rate**: > 50% (critical), > 20% (warning)
- **Slow execution**: > 30 seconds average

## Troubleshooting

### Common Issues

#### 1. No Log Output

```bash
# Check if logs directory exists
ls -la logs/

# Verify log level
echo $LOG_LEVEL

# Check file permissions
ls -la logs/
```

#### 2. High Error Rates

```bash
# Check error patterns
npm run monitor:errors

# View recent errors
tail -f logs/cron-error-$(date +%Y-%m-%d).log
```

#### 3. Performance Issues

```bash
# Check execution times
npm run monitor:health

# Monitor memory usage
grep "Memory usage" logs/cron-debug-$(date +%Y-%m-%d).log
```

### Debug Mode

Enable detailed debugging:

```bash
LOG_LEVEL=debug npm run start:cron
```

This will log:

- Every SQS message body
- API request/response details
- Memory usage at each step
- Detailed timing information

## Best Practices

### 1. Regular Monitoring

- Check health status daily: `npm run monitor:health`
- Review error patterns weekly: `npm run monitor:errors`
- Monitor performance trends monthly

### 2. Log Management

- Logs rotate automatically, but monitor disk usage
- Archive important error logs for compliance
- Set up alerts for critical error patterns

### 3. Performance Optimization

- Monitor average execution times
- Watch for memory leaks in long-running processes
- Optimize based on message processing patterns

### 4. Error Handling

- Review error patterns to identify systemic issues
- Implement retry logic for transient failures
- Set up dead letter queues for persistent failures

## Environment Variables

Configure logging behavior:

```bash
# Log level (error, warn, info, debug)
LOG_LEVEL=info

# Timezone for log timestamps
TZ=America/New_York

# Custom log directory (optional)
LOG_DIR=/custom/path/to/logs
```

## Integration with External Systems

### Log Aggregation

The structured JSON logs can be easily integrated with:

- **ELK Stack** (Elasticsearch, Logstash, Kibana)
- **Splunk**
- **CloudWatch Logs**
- **Datadog**

### Alerting

Set up alerts based on:

- Error rate thresholds
- Execution time anomalies
- Missing heartbeats (stale executions)

### Metrics Export

The `metrics.json` file can be consumed by monitoring systems for:

- Performance dashboards
- Trend analysis
- Capacity planning

## Sample Log Entries

### Successful Execution

```json
{
  "timestamp": "2024-01-15 10:30:15.123",
  "level": "INFO",
  "message": "Cron job execution completed successfully",
  "cronId": "abc123",
  "executionTimeMs": 2500,
  "result": {
    "totalMessagesProcessed": 5,
    "totalErrors": 0,
    "totalBatches": 1
  }
}
```

### Error Example

```json
{
  "timestamp": "2024-01-15 10:30:20.456",
  "level": "ERROR",
  "message": "Failed to process SQS message",
  "messageId": "msg-456",
  "error": "HTTP 500: Internal Server Error",
  "stack": "Error: HTTP 500...",
  "context": {
    "projectId": "proj-123",
    "documentName": "doc.pdf"
  }
}
```

## Quick Start

1. **Install dependencies** (already done):

   ```bash
   yarn add winston winston-daily-rotate-file
   ```

2. **Start the cron job with enhanced logging**:

   ```bash
   npm run start:cron
   ```

3. **Monitor in real-time**:

   ```bash
   # In another terminal
   npm run monitor
   ```

4. **View live logs**:
   ```bash
   tail -f logs/cron-$(date +%Y-%m-%d).log
   ```

## Production Deployment

### Recommended Settings

```bash
# Production environment variables
NODE_ENV=production
LOG_LEVEL=info
TZ=UTC

# Enable log compression and rotation
LOG_COMPRESS=true
LOG_MAX_SIZE=20m
LOG_MAX_FILES=30d
```

### Process Management

Use PM2 or similar for production:

```bash
# Install PM2
npm install -g pm2

# Start with PM2
pm2 start ecosystem.config.js

# Monitor with PM2
pm2 monit

# View logs
pm2 logs cron-job
```

## Support

For issues with the monitoring system:

1. Check the logs first: `npm run monitor:logs`
2. Verify health status: `npm run monitor:health`
3. Review error patterns: `npm run monitor:errors`
4. Enable debug logging if needed: `LOG_LEVEL=debug`

## What's New

### Enhanced Features Added:

✅ **Structured JSON logging** with Winston
✅ **Automatic log rotation** and compression
✅ **Performance monitoring** with execution time tracking
✅ **Memory usage monitoring**
✅ **Error pattern analysis** and categorization
✅ **Health checks** with automated alerts
✅ **CLI monitoring tools** for real-time insights
✅ **Comprehensive API call logging**
✅ **SQS message processing tracking**
✅ **Graceful shutdown handling**
✅ **Metrics collection and analysis**

### Before vs After:

**Before**: Basic `console.log` statements
**After**: Professional logging system with monitoring, health checks, and analytics
