# Environment Configuration Example
# Copy this file to .env and update with your actual values

# AWS Configuration
NEXT_PUBLIC_AWS_REGION=us-east-1
NEXT_PUBLIC_AWS_PROJECT_REGION=us-east-1
NEXT_PUBLIC_AWS_COGNITO_REGION=us-east-1
NEXT_PUBLIC_AWS_COGNITO_IDENTITY_POOL_ID=your-identity-pool-id
NEXT_PUBLIC_AWS_USER_POOLS_ID=your-user-pool-id
NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID=your-web-client-id

# SQS Configuration
NEXT_PUBLIC_SQS_CONSUME_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/123456789012/your-queue-name

# API Configuration
NEXT_PUBLIC_API_DOMAIN=https://your-api-domain.com
NEXT_PUBLIC_QNA_API_DOMAIN=https://your-qna-api-domain.com

# File Upload Limits
NEXT_PUBLIC_MAX_MEDICAL_FILE_SIZE=10485760
NEXT_PUBLIC_MAX_PAGES_LIMIT=100
NEXT_PUBLIC_MAX_PROJECTS=10

# Database Configuration (if applicable)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=your-database-name
DB_USER=your-username
DB_PASSWORD=your-password

# Logging Configuration
LOG_LEVEL=info
# Options: error, warn, info, debug

# Timezone for logs (optional)
TZ=America/New_York

# Node Environment
NODE_ENV=development
# Options: development, production, test

# Cron Job Configuration
CRON_ENABLED=true
CRON_EXPRESSION="*/5 * * * * *"
# Default: every 5 seconds

# Monitoring Configuration
HEALTH_CHECK_ENABLED=true
METRICS_RETENTION_DAYS=30
