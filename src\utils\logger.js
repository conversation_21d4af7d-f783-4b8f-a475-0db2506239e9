const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');

// Custom format for structured logging
const customFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let logMessage = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      logMessage += ` | ${JSON.stringify(meta)}`;
    }
    
    return logMessage;
  })
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss.SSS'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let logMessage = `${timestamp} ${level}: ${message}`;
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      logMessage += ` ${JSON.stringify(meta, null, 2)}`;
    }
    
    return logMessage;
  })
);

// Create the logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: customFormat,
  defaultMeta: { service: 'cron-job' },
  transports: [
    // Console transport for development
    new winston.transports.Console({
      format: consoleFormat,
      level: 'debug'
    }),
    
    // Daily rotate file for all logs
    new DailyRotateFile({
      filename: path.join(logsDir, 'cron-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '14d',
      level: 'info'
    }),
    
    // Separate file for errors
    new DailyRotateFile({
      filename: path.join(logsDir, 'cron-error-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '20m',
      maxFiles: '30d',
      level: 'error'
    }),
    
    // Debug file for detailed debugging
    new DailyRotateFile({
      filename: path.join(logsDir, 'cron-debug-%DATE%.log'),
      datePattern: 'YYYY-MM-DD',
      zippedArchive: true,
      maxSize: '50m',
      maxFiles: '7d',
      level: 'debug'
    })
  ],
  
  // Handle uncaught exceptions and rejections
  exceptionHandlers: [
    new winston.transports.File({ 
      filename: path.join(logsDir, 'exceptions.log') 
    })
  ],
  rejectionHandlers: [
    new winston.transports.File({ 
      filename: path.join(logsDir, 'rejections.log') 
    })
  ]
});

// Helper functions for structured logging
const createLogContext = (operation, metadata = {}) => ({
  operation,
  timestamp: new Date().toISOString(),
  ...metadata
});

const logExecutionTime = (startTime, operation, metadata = {}) => {
  const executionTime = Date.now() - startTime;
  logger.info(`${operation} completed`, {
    ...metadata,
    executionTimeMs: executionTime,
    executionTimeFormatted: `${executionTime}ms`
  });
  return executionTime;
};

const logMemoryUsage = (operation) => {
  const memUsage = process.memoryUsage();
  logger.debug(`Memory usage for ${operation}`, {
    rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
    heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
    heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
    external: `${Math.round(memUsage.external / 1024 / 1024)}MB`
  });
};

// Performance monitoring wrapper
const withPerformanceLogging = async (operation, fn, metadata = {}) => {
  const startTime = Date.now();
  const context = createLogContext(operation, metadata);
  
  logger.info(`Starting ${operation}`, context);
  logMemoryUsage(operation);
  
  try {
    const result = await fn();
    const executionTime = logExecutionTime(startTime, operation, { 
      ...context, 
      success: true 
    });
    
    return { result, executionTime };
  } catch (error) {
    const executionTime = Date.now() - startTime;
    logger.error(`${operation} failed`, {
      ...context,
      success: false,
      executionTimeMs: executionTime,
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
};

// API call logging wrapper
const logApiCall = async (url, method, body, fn) => {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(7);
  
  logger.debug('API call initiated', {
    requestId,
    url,
    method,
    body: body ? JSON.stringify(body) : undefined
  });
  
  try {
    const response = await fn();
    const executionTime = Date.now() - startTime;
    
    logger.info('API call successful', {
      requestId,
      url,
      method,
      executionTimeMs: executionTime,
      status: response.status || 'unknown'
    });
    
    return response;
  } catch (error) {
    const executionTime = Date.now() - startTime;
    
    logger.error('API call failed', {
      requestId,
      url,
      method,
      executionTimeMs: executionTime,
      error: error.message,
      stack: error.stack
    });
    
    throw error;
  }
};

// SQS message processing logger
const logSqsMessage = (message, action, metadata = {}) => {
  const messageId = message.MessageId || 'unknown';
  const receiptHandle = message.ReceiptHandle ? 
    message.ReceiptHandle.substring(0, 20) + '...' : 'unknown';
  
  logger.info(`SQS message ${action}`, {
    messageId,
    receiptHandle,
    bodyLength: message.Body ? message.Body.length : 0,
    attributes: message.MessageAttributes || {},
    ...metadata
  });
};

// Export the logger and helper functions
module.exports = {
  logger,
  createLogContext,
  logExecutionTime,
  logMemoryUsage,
  withPerformanceLogging,
  logApiCall,
  logSqsMessage
};
